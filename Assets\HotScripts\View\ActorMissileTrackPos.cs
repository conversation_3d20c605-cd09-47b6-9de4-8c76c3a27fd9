// ReSharper disable ClassWithVirtualMembersNeverInherited.Global

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;
using Apq.Utils;

using Cysharp.Threading.Tasks;

using HotScripts;

using RxEventsM2V;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     角色阵营的抛物线子弹（定点打击）
    /// </summary>
    public class ActorMissileTrackPos : ActorBulletBase
    {
        /// <summary>
        ///     抛物线轨迹生成器
        /// </summary>
        public ParabolicLocusGenerator ParabolicLocusGenerator { get; set; }

        /// <summary>
        ///     抛物线轨迹路径点
        /// </summary>
        public List<Vector3> ParabolicPath { get; set; }

        /// <inheritdoc />
        public override async UniTask OnAfterInitView()
        {
            //Debug.Log($"88888 抛物线子弹初始化开始 - BulletId:{GetHashCode()} Position:{transform.position} TrackPos:{BulletThing.TrackPosition}");
            
            await base.OnAfterInitView();

            // 确保子弹位置正确设置为起始位置
            if (BulletThing.Position != Vector3.zero)
            {
                transform.position = BulletThing.Position;
                //Debug.Log($"88888 抛物线子弹位置修正 - BulletId:{GetHashCode()} 从BulletThing设置Position:{BulletThing.Position}");
            }

            // 初始化抛物线轨迹生成器
            ParabolicLocusGenerator = new ParabolicLocusGenerator
            {
                StartPosition = transform.position,
                TargetPosition = BulletThing.TrackPosition,
                ApexHeight = 36f, // 使用用户修改的新值
                ParabolicWidth = 38f, // 使用用户修改的新值
                SegmentCount = 60 // 增加分段数，让轨迹更平滑
            };

            // 计算抛物线路径
            if (BulletThing.TrackPosition.HasValue)
            {
                ParabolicPath = ParabolicLocusGenerator.CalcParabolicPath(transform.position);
                //Debug.Log($"88888 抛物线子弹路径计算完成 - BulletId:{GetHashCode()} StartPos:{transform.position} TargetPos:{BulletThing.TrackPosition} ApexHeight:{ParabolicLocusGenerator.ApexHeight} PathCount:{ParabolicPath?.Count}");
                
                // 输出抛物线参数信息
                   ///     Debug.Log($"99999 抛物线子弹参数 - BulletId:{GetHashCode()} ApexHeight:{ParabolicLocusGenerator.ApexHeight} ParabolicWidth:{ParabolicLocusGenerator.ParabolicWidth} SegmentCount:{ParabolicLocusGenerator.SegmentCount}");
            }
            else
            {
                //Debug.LogError($"88888 抛物线子弹初始化失败 - BulletId:{GetHashCode()} TrackPosition为空");
                return;
            }

            // 显示子弹图片 - 这是关键步骤，确保子弹可见
            if (ImgElement != null)
            {
                ImgElement.SetActive(true);
                //Debug.Log($"88888 抛物线子弹显示图片 - BulletId:{GetHashCode()} ImgElement已激活");
            }
            else
            {
                //Debug.LogError($"88888 抛物线子弹显示失败 - BulletId:{GetHashCode()} ImgElement为空");
            }

            // 设置子弹在正确的渲染层级
            if (SpriteRenderer != null)
            {
                SetSortingLayer("PlayerBullet", 10); // 提高渲染优先级确保可见
                //Debug.Log($"88888 抛物线子弹设置渲染层级 - BulletId:{GetHashCode()} SortingLayer:PlayerBullet Order:10");
            }
            else
            {
                //Debug.LogError($"88888 抛物线子弹渲染失败 - BulletId:{GetHashCode()} SpriteRenderer为空");
            }

            // 确保游戏对象激活
            if (!gameObject.activeInHierarchy)
            {
                gameObject.SetActive(true);
                //Debug.Log($"88888 抛物线子弹激活游戏对象 - BulletId:{GetHashCode()}");
            }
        }

        /// <inheritdoc />
        public override void StartMove()
        {
            //Debug.Log($"88888 抛物线子弹开始移动 - BulletId:{GetHashCode()} Position:{transform.position} TrackPos:{BulletThing.TrackPosition}");
            // 启动抛物线子弹的移动逻辑
            StartParabolicMove().Forget();
        }

        /// <summary>
        /// 启动垂直轰炸移动（停留后垂直下落）
        /// </summary>
        private async UniTaskVoid StartParabolicMove()
        {
            try
            {
                CancellationToken token = this.GetCancellationTokenOnDestroy();

                // 获取停留时间参数
                float stayTime = GetStayTimeFromRemark();
                MoveDuration.Value = 0f;

                //Debug.Log($"88888 垂直轰炸子弹移动开始 - BulletId:{GetHashCode()} StayTime:{stayTime}s StartPos:{transform.position}");

                // 第一阶段：停留
                if (stayTime > 0)
                {
                    //Debug.Log($"88888 垂直轰炸子弹开始停留 - BulletId:{GetHashCode()} StayTime:{stayTime}s");
                    await UniTask.Delay(TimeSpan.FromSeconds(stayTime), cancellationToken: token);
                    //Debug.Log($"88888 垂直轰炸子弹停留结束 - BulletId:{GetHashCode()}");
                }

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 第二阶段：垂直下落
                //Debug.Log($"88888 垂直轰炸子弹开始下落 - BulletId:{GetHashCode()} StartPos:{transform.position} TargetPos:{BulletThing.TrackPosition}");
                
                int frameCount = 0;
                while (!token.IsCancellationRequested)
                {
                    float deltaTime = Time.deltaTime;
                    frameCount++;
                    
                    try
                    {
                        // 垂直下落移动
                        bool reachedTarget = MoveOne_VerticalFall(deltaTime);
                        
                        // 每帧都输出移动日志
                        if (frameCount % 10 == 0) // 每10帧输出一次
                        {
                            //Debug.Log($"88888 垂直轰炸子弹下落帧{frameCount} - BulletId:{GetHashCode()} Pos:{transform.position} TargetY:{BulletThing.TrackPosition?.y} ReachedTarget:{reachedTarget}");
                        }
                        
                        if (reachedTarget)
                        {
                            //Debug.Log($"88888 垂直轰炸子弹到达目标 - BulletId:{GetHashCode()} FinalPos:{transform.position} TotalFrames:{frameCount}");
                            // 到达目标点，触发爆炸
                            DoExplose(token);
                            break;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        //Debug.LogError($"88888 垂直轰炸子弹移动异常 - BulletId:{GetHashCode()} Frame:{frameCount} {ex.Message}");
                    }

                    await UniTask.NextFrame(token);
                }
            }
            catch (OperationCanceledException)
            {
                //Debug.Log($"88888 垂直轰炸子弹移动取消 - BulletId:{GetHashCode()}");
                throw;
            }
            catch (MissingReferenceException)
            {
                //Debug.Log($"88888 垂直轰炸子弹引用丢失 - BulletId:{GetHashCode()}");
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 垂直轰炸子弹移动失败 - BulletId:{GetHashCode()} {ex.Message}");
            }
            finally
            {
                //Debug.Log($"88888 垂直轰炸子弹移动结束 - BulletId:{GetHashCode()}");
                OnMoveEnd();
            }
        }

        /// <summary>
        /// 计算抛物线飞行总时长
        /// </summary>
        /// <returns>飞行时长（秒）</returns>
        private float CalculateFlightDuration()
        {
            // 优先从枪的配置中获取子弹速度
            float bulletSpeed = 0f;
            
            // 尝试多种方式获取子弹速度
            if (GunThing != null)
            {
                bulletSpeed = (float)GunThing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                //Debug.Log($"88888 抛物线子弹速度获取 - BulletId:{GetHashCode()} 从GunThing获取速度:{bulletSpeed}");
            }
            
            if (bulletSpeed <= 0)
            {
                bulletSpeed = (float)BulletThing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                //Debug.Log($"88888 抛物线子弹速度获取 - BulletId:{GetHashCode()} 从BulletThing获取速度:{bulletSpeed}");
            }
            
            if (bulletSpeed <= 0 && BulletThing.CdExecutor?.Thing != null)
            {
                bulletSpeed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                //Debug.Log($"88888 抛物线子弹速度获取 - BulletId:{GetHashCode()} 从CdExecutor.Thing获取速度:{bulletSpeed}");
            }
            
            if (bulletSpeed <= 0)
            {
                bulletSpeed = 10f; // 默认速度
                //Debug.Log($"88888 抛物线子弹速度获取 - BulletId:{GetHashCode()} 使用默认速度:{bulletSpeed}");
            }

            float duration = 2f; // 默认飞行时间
            if (BulletThing.TrackPosition.HasValue && ParabolicLocusGenerator != null)
            {
                float distance = Vector3.Distance(ParabolicLocusGenerator.StartPosition, BulletThing.TrackPosition.Value);
                duration = distance / bulletSpeed;
                //Debug.Log($"88888 抛物线子弹飞行时间计算 - BulletId:{GetHashCode()} Distance:{distance} Speed:{bulletSpeed} Duration:{duration}s");
            }
            else
            {
                //Debug.Log($"88888 抛物线子弹飞行时间 - BulletId:{GetHashCode()} 使用默认时间:{duration}s TrackPos:{BulletThing.TrackPosition}");
            }

            return duration;
        }

        /// <summary>
        /// 从子弹Remark属性获取停留时间
        /// </summary>
        /// <returns>停留时间（秒）</returns>
        private float GetStayTimeFromRemark()
        {
            try
            {
                // 从子弹的Remark属性中获取停留时间
                var remarkValues = BulletThing.GetTotalString(PropType.Remark);
                if (remarkValues != null && remarkValues.Count > 0)
                {
                    string remarkStr = remarkValues.FirstOrDefault();
                    if (!string.IsNullOrEmpty(remarkStr) && float.TryParse(remarkStr, out float stayTime))
                    {
                        //Debug.Log($"88888 垂直轰炸子弹获取停留时间 - BulletId:{GetHashCode()} Remark:{remarkStr} StayTime:{stayTime}s");
                        return stayTime;
                    }
                }
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 垂直轰炸子弹获取停留时间失败 - BulletId:{GetHashCode()} {ex.Message}");
            }

            //Debug.Log($"88888 垂直轰炸子弹使用默认停留时间 - BulletId:{GetHashCode()} DefaultStayTime:1s");
            return 1f; // 默认停留1秒
        }

        /// <summary>
        /// 垂直下落移动一次
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        /// <returns>是否已到达终点</returns>
        private bool MoveOne_VerticalFall(float deltaTime)
        {
            if (!BulletThing.TrackPosition.HasValue)
            {
                //Debug.LogError($"88888 垂直轰炸子弹下落失败 - BulletId:{GetHashCode()} TrackPosition为空");
                return true;
            }

            Vector3 currentPos = transform.position;
            Vector3 targetPos = BulletThing.TrackPosition.Value;
            
            // 检查是否已到达目标Y坐标
            if (currentPos.y <= targetPos.y)
            {
                // 设置最终位置
                transform.position = new Vector3(currentPos.x, targetPos.y, currentPos.z);
                BulletThing.Position = transform.position;
                
                //Debug.Log($"88888 垂直轰炸子弹到达目标Y - BulletId:{GetHashCode()} CurrentY:{currentPos.y} TargetY:{targetPos.y} FinalPos:{transform.position}");
                return true;
            }

            // 获取下落速度
            float fallSpeed = GetFallSpeed();
            
            // 计算新位置（只改变Y坐标，垂直下落）
            float newY = currentPos.y - fallSpeed * deltaTime;
            Vector3 newPos = new Vector3(currentPos.x, newY, currentPos.z);
            
            // 更新位置
            transform.position = newPos;
            BulletThing.Position = newPos;
            
            // 设置水平向左的朝向
            transform.right = Vector3.left;
            
            // 检查碰撞（如果碰到怪物）
            CheckCollisionWithMonsters();
            
            return false;
        }

        /// <summary>
        /// 获取下落速度
        /// </summary>
        /// <returns>下落速度</returns>
        private float GetFallSpeed()
        {
            // 优先从枪的配置中获取子弹速度作为下落速度
            float fallSpeed = 0f;
            
            if (GunThing != null)
            {
                fallSpeed = (float)GunThing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            }
            
            if (fallSpeed <= 0)
            {
                fallSpeed = (float)BulletThing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            }
            
            if (fallSpeed <= 0 && BulletThing.CdExecutor?.Thing != null)
            {
                fallSpeed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            }
            
            if (fallSpeed <= 0)
            {
                fallSpeed = 50f; // 默认下落速度
            }
            
            return fallSpeed;
        }

        /// <summary>
        /// 检查与怪物的碰撞
        /// </summary>
        private void CheckCollisionWithMonsters()
        {
            try
            {
                Vector3 currentPos = transform.position;
                
                // 查找附近的怪物
                var nearbyMonsters = SingletonMgr.Instance.BattleMgr.Monsters
                    .Where(monster => monster.Area2D.Count > 0 &&
                                    Vector3.Distance(monster.Position, currentPos) <= 2f) // 2单位碰撞检测范围
                    .ToList();

                foreach (var monster in nearbyMonsters)
                {
                    // 计算伤害
                    (double damage, bool isCritical) = Helper.CalcDamage(BulletThing, 0, 1, monster);

                    if (damage > 0)
                    {
                        //Debug.Log($"88888 垂直轰炸子弹碰撞伤害 - BulletId:{GetHashCode()} Monster:{monster.GetHashCode()} Damage:{damage} IsCritical:{isCritical}");
                        // 造成伤害
                        monster.TakeHit(BulletThing, damage, isCritical);
                    }
                }
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 垂直轰炸子弹碰撞检测失败 - BulletId:{GetHashCode()} {ex.Message}");
            }
        }

        /// <summary>
        /// 按抛物线轨迹移动一次（已废弃，保留用于兼容性）
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        /// <param name="totalDuration">总飞行时长</param>
        /// <returns>是否已到达终点</returns>
        private bool MoveOne_ParabolicMove(float deltaTime, float totalDuration)
        {
            if (totalDuration <= 0)
            {
                //Debug.Log($"88888 抛物线子弹移动完成 - BulletId:{GetHashCode()} TotalDuration为0");
                return true;
            }

            // 移动计时
            MoveDuration.Value += deltaTime;

            // 时间进度
            float progress = Mathf.Clamp01(MoveDuration.Value / totalDuration);

            // 确保抛物线生成器和目标位置正确
            if (ParabolicLocusGenerator != null && BulletThing.TrackPosition.HasValue)
            {
                // 更新起始位置（只在初始阶段）
                if (progress < 0.01f && ParabolicLocusGenerator.StartPosition != BulletThing.Position)
                {
                    ParabolicLocusGenerator.StartPosition = BulletThing.Position;
                    //Debug.Log($"88888 抛物线子弹更新起始位置 - BulletId:{GetHashCode()} NewStartPos:{BulletThing.Position}");
                }

                // 获取当前应该在的位置
                Vector3 currentPos = ParabolicLocusGenerator.GetPositionAtProgress(progress);
                Vector3 tangent = ParabolicLocusGenerator.GetTangentAtProgress(progress);

                // 更新子弹位置
                transform.position = currentPos;
                
                // 更新子弹朝向（沿切线方向）
                if (tangent != Vector3.zero)
                {
                    transform.right = tangent.normalized;
                }

                // 添加子弹缩放效果：发射→最高点逐步放大，最高点→终点逐步变小
                float scale = CalculateBulletScale(progress);
                transform.localScale = Vector3.one * scale;
                
                // 添加子弹旋转效果，模拟真实抛物线飞行
                float rotation = CalculateBulletRotation(progress, tangent);
                transform.rotation = Quaternion.Euler(0, 0, rotation);

                // 更新BulletThing的位置数据
                BulletThing.Position = currentPos;

                // 确保子弹图片可见
                if (ImgElement != null && !ImgElement.activeInHierarchy)
                {
                    ImgElement.SetActive(true);
                    //Debug.Log($"88888 抛物线子弹重新显示图片 - BulletId:{GetHashCode()} Progress:{progress:F3}");
                }

                // 详细的移动日志（降低频率避免刷屏）
                if (Time.frameCount % 30 == 0) // 每30帧输出一次
                {
                   ///         Debug.Log($"88888 抛物线子弹移动中 - BulletId:{GetHashCode()} Progress:{progress:F3} Duration:{MoveDuration.Value:F2}/{totalDuration:F2} Pos:{currentPos} Tangent:{tangent} Scale:{scale:F2} Rotation:{rotation:F1} ImgActive:{ImgElement?.activeInHierarchy} GameObjActive:{gameObject.activeInHierarchy}");
                }
            }
            else
            {
                //Debug.LogError($"88888 抛物线子弹移动失败 - BulletId:{GetHashCode()} Generator:{ParabolicLocusGenerator != null} TrackPos:{BulletThing.TrackPosition}");
                return true; // 数据异常，结束移动
            }

            return progress >= 1f;
        }

        /// <summary>
        /// 计算子弹缩放比例
        /// </summary>
        /// <param name="progress">时间进度</param>
        /// <returns>缩放比例</returns>
        private float CalculateBulletScale(float progress)
        {
            // 子弹缩放效果：发射→最高点逐步放大，最高点→终点逐步变小
            // 最高点在progress=0.5处
            
            float baseScale = 1f;
            float maxScale = 1.8f; // 最大缩放倍数
            float minScale = 0.8f;  // 最小缩放倍数
            
            if (progress <= 0.5f)
            {
                // 前半段：从基础大小放大到最大
                float scaleProgress = progress * 2f; // 0-1
                return Mathf.Lerp(baseScale, maxScale, scaleProgress);
            }
            else
            {
                // 后半段：从最大缩小到最小
                float scaleProgress = (progress - 0.5f) * 2f; // 0-1
                return Mathf.Lerp(maxScale, minScale, scaleProgress);
            }
        }

        /// <summary>
        /// 计算子弹旋转角度
        /// </summary>
        /// <param name="progress">时间进度</param>
        /// <param name="tangent">切线方向</param>
        /// <returns>旋转角度</returns>
        private float CalculateBulletRotation(float progress, Vector3 tangent)
        {
            // 根据切线方向计算子弹的旋转角度，模拟真实飞行
            float baseRotation = Mathf.Atan2(tangent.y, tangent.x) * Mathf.Rad2Deg;
            
            // 添加额外的旋转效果，模拟子弹在空中的自旋
            float spinRotation = progress * 360f * 2f; // 飞行过程中自旋2圈
            
            return baseRotation + spinRotation;
        }

        /// <summary>
        /// 在目标位置爆炸后归还到子弹池
        /// </summary>
        /// <param name="token"></param>
        private void DoExplose(CancellationToken token)
        {
            //Debug.Log($"88888 垂直轰炸子弹开始爆炸检查 - BulletId:{GetHashCode()} CurrentPos:{transform.position}");
            
            Vector3 explosePos = transform.position; // 使用当前位置作为爆炸位置

            // 根据CommonProp表字段PropType=ExplosePriority触发爆炸逻辑
            // 无论是否碰到怪物，都会触发爆炸
            double exploseRate = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault();
            
            // 如果配置了爆炸概率，按概率触发爆炸；如果没有配置或为0，则必定爆炸
            bool shouldExplode = exploseRate <= 0 || RandomNum.RandomDouble(0, 1) <= exploseRate;
            
            //Debug.Log($"88888 垂直轰炸子弹爆炸概率检查 - BulletId:{GetHashCode()} ExploseRate:{exploseRate} ShouldExplode:{shouldExplode}");
            
            if (shouldExplode)
            {
                // 爆炸声音
                string exploseSound = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseSound).FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(exploseSound))
                {
                    AudioPlayer.Instance.PlaySound(exploseSound).Forget();
                }

                // 爆炸特效
                string exploseEffect = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault();
                float exploseRadius = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();

                //Debug.Log($"88888 垂直轰炸子弹爆炸效果 - BulletId:{GetHashCode()} Sound:{exploseSound} Effect:{exploseEffect} Radius:{exploseRadius} ExplosePos:{explosePos}");

                if (!string.IsNullOrWhiteSpace(exploseEffect))
                {
                    EffectMgr.Instance.ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect),
                        explosePos, exploseRadius).Forget();
                }

                // 检查爆炸半径内的敌人
                if (exploseRadius > 0)
                {
                    // 查找爆炸范围内的怪物
                    var monstersInRange = SingletonMgr.Instance.BattleMgr.Monsters
                        .Where(monster => monster.Area2D.Count > 0 &&
                                        Vector3.Distance(monster.Position, explosePos) <= exploseRadius)
                        .ToList();

                    //Debug.Log($"88888 垂直轰炸子弹爆炸伤害 - BulletId:{GetHashCode()} MonstersInRange:{monstersInRange.Count} ExplosePos:{explosePos}");

                    // 对范围内的每个怪物造成伤害
                    foreach (var monster in monstersInRange)
                    {
                        // 计算伤害
                        (double damage, bool isCritical) = Helper.CalcDamage(BulletThing, 0, 1, monster);

                        if (damage > 0)
                        {
                            //Debug.Log($"88888 垂直轰炸子弹造成伤害 - BulletId:{GetHashCode()} Monster:{monster.GetHashCode()} Damage:{damage} IsCritical:{isCritical}");
                            // 造成伤害
                            monster.TakeHit(BulletThing, damage, isCritical);
                        }
                    }
                }
            }

            //Debug.Log($"88888 垂直轰炸子弹回池 - BulletId:{GetHashCode()}");
            // 还给子弹池
            TurnToPool().Forget();
        }

        /// <inheritdoc />
        protected override bool OnBeforeMoveOne()
        {
            // 子弹销毁了或隐藏了，结束
            if (!this || !isActiveAndEnabled)
            {
                return true;
            }

            return false;
        }

        /// <inheritdoc />
        protected override void OnMoveEnd()
        {
            try
            {
                // 还给子弹池
                TurnToPool().Forget();
            }
            catch
            {
                // ignored
            }
        }

        /// <inheritdoc />
        public override IList<HitThingCells> DoCollideEnemies(LineSegment line)
        {
            // 抛物线子弹在飞行过程中不与敌人碰撞，只在到达终点时爆炸
            return new List<HitThingCells>();
        }
    }
}