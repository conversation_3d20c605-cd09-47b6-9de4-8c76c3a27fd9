// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     角色阵营的抛物线子弹射击
    /// </summary>
    public class ThingCdExecutor_6 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            //Debug.Log($"88888 抛物线射击开始 - Actor:{Actor?.GetHashCode()} Thing:{Thing?.GetHashCode()} ShootMethod:6");
            
            // 使用新的目标选择逻辑
            var (targetEnemy, targetPos) = FindParabolicTarget();

            // 确保有目标位置，即使没有找到敌人也要有默认位置
            //Debug.Log($"88888 抛物线射击目标确定 - Actor:{Actor?.GetHashCode()} Target:{targetEnemy?.GetHashCode()} TargetPos:{targetPos}");

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                //Debug.Log($"88888 抛物线射击失败 - Actor为空");
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向始终是向X轴正方向
                Vector3 shootDir = Vector3.right;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                //Debug.Log($"88888 抛物线射击配置 - ShootTimes:{shootTimes} DelayCount:{burstDelayList.Count} BulletCountList:{string.Join(",", burstBulletCountList)} AnglesIds:{string.Join(",", burstAnglesIds)} ShootDir:{shootDir}");

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        //Debug.Log($"88888 抛物线射击跳过 - 第{i}轮缺少子弹数量配置");
                        return i;
                    }

                    long bulletQty = burstBulletCountList[i];

                    //Debug.Log($"88888 抛物线射击启动第{i}轮 - Delay:{x}s BulletQty:{bulletQty} TargetPos:{targetPos} AngleId:{burstAnglesIds.IndexOf_ByCycle(i)} (轮次间延时:{x}s, 轮内{bulletQty}颗子弹同时发射)");

                    // BurstDelayList用于不同轮次之间的延时，同一轮内的多颗子弹同时发射
                    BurstOne(cts_Skill.Token, (float)x, targetEnemy, targetPos, shootDir, bulletQty,
                            burstAnglesIds.IndexOf_ByCycle(i), burstDelayList)
                        .Forget();
                    return i;
                }).ToList();
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 抛物线射击异常 - {ex.Message} StackTrace:{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 计算抛物线子弹的目标坐标
        /// </summary>
        /// <param name="target">目标敌人</param>
        /// <returns>计算后的目标坐标</returns>
        private Vector3 CalculateParabolicTarget(ThingBase target)
        {
            // 角色当前位置
            Vector3 actorPos = Actor.Position;
            
            // 枪的射程
            float gunRange = (float)Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            
            // 基础终点：角色X坐标+8（向右射击的最小距离）
            float baseTargetX = actorPos.x + 8f;
            
            // 目标怪物的X坐标
            float monsterX = target.Position.x;
            
            // 终点X坐标：在角色X+8和目标怪物X之间
            float targetX = Mathf.Max(baseTargetX, monsterX);
            
            // 限制在射程范围内
            float maxX = actorPos.x + gunRange;
            targetX = Mathf.Min(targetX, maxX);
            
            // 终点坐标：Y轴保持角色当前Y坐标，X轴使用计算的目标X（X是射击距离变量）
            return new Vector3(targetX, actorPos.y, actorPos.z);
        }

        /// <summary>
        /// 查找最适合的抛物线攻击目标
        /// </summary>
        /// <returns>目标怪物和目标坐标</returns>
        private (ThingBase target, Vector3 targetPos) FindParabolicTarget()
        {
            // 角色当前位置
            Vector3 actorPos = Actor.Position;
            float gunRange = (float)Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            float minTargetX = actorPos.x + 8f; // 最小目标X坐标（向右射击的最小距离）
            float maxTargetX = actorPos.x + gunRange; // 最大目标X坐标

            Debug.Log($"111111111 抛物线目标搜索开始 - ActorPos:{actorPos} GunRange:{gunRange} TargetXRange:[{minTargetX}, {maxTargetX}]");

            // 获取所有活跃的怪物
            var allMonsters = SingletonMgr.Instance.BattleMgr.Monsters
                .Where(m => m != null && m.ThingBehaviour != null && m.ThingBehaviour.gameObject.activeInHierarchy)
                .ToList();

            Debug.Log($"111111111 抛物线目标搜索 - 总怪物数:{allMonsters.Count}");

            if (allMonsters.Count == 0)
            {
                // 没有怪物，使用GunRange最大值作为默认目标位置
                Vector3 defaultTarget = new Vector3(maxTargetX, actorPos.y, actorPos.z);
                Debug.Log($"111111111 抛物线目标搜索 - 无怪物，使用GunRange最大值目标:{defaultTarget} (X:{maxTargetX} = 角色X:{actorPos.x} + GunRange:{gunRange})");
                return (null, defaultTarget);
            }

            // 过滤满足X坐标条件的怪物（在射击方向上）
            var validMonsters = allMonsters
                .Where(m => m.Position.x >= minTargetX && m.Position.x <= maxTargetX)
                .ToList();

            Debug.Log($"111111111 抛物线目标搜索 - X范围内怪物数:{validMonsters.Count}");

            ThingBase bestTarget = null;
            float minDistance = float.MaxValue;

            if (validMonsters.Count > 0)
            {
                // 从满足X坐标条件的怪物中选择与角色X坐标距离最近的
                foreach (var monster in validMonsters)
                {
                    float xDistance = Mathf.Abs(monster.Position.x - actorPos.x);
                    if (xDistance < minDistance)
                    {
                        minDistance = xDistance;
                        bestTarget = monster;
                    }
                }
                
                Debug.Log($"111111111 抛物线目标搜索 - X范围内最近怪物:{bestTarget?.GetHashCode()} XDistance:{minDistance} MonsterPos:{bestTarget?.Position}");
            }
            else
            {
                // 没有满足X条件的怪物，从所有怪物中选择X坐标距离最近的
                foreach (var monster in allMonsters)
                {
                    float xDistance = Mathf.Abs(monster.Position.x - actorPos.x);
                    if (xDistance < minDistance)
                    {
                        minDistance = xDistance;
                        bestTarget = monster;
                    }
                }
                
                Debug.Log($"111111111 抛物线目标搜索 - 全范围最近怪物:{bestTarget?.GetHashCode()} XDistance:{minDistance} MonsterPos:{bestTarget?.Position}");
            }

            Vector3 targetPos;
            if (bestTarget != null)
            {
                // 计算目标坐标：Y为角色Y坐标，X为限制在范围内的怪物X坐标（X是射击距离变量）
                float targetX = Mathf.Clamp(bestTarget.Position.x, minTargetX, maxTargetX);
                targetPos = new Vector3(targetX, actorPos.y, actorPos.z);
                
                Debug.Log($"111111111 抛物线目标搜索 - 最终目标:{bestTarget.GetHashCode()} 原始X:{bestTarget.Position.x} 目标X:{targetX} 目标坐标:{targetPos}");
            }
            else
            {
                // 没有找到怪物，使用GunRange最大值作为默认目标位置
                targetPos = new Vector3(maxTargetX, actorPos.y, actorPos.z);
                Debug.Log($"111111111 抛物线目标搜索 - 未找到怪物，使用GunRange最大值目标:{targetPos}");
            }

            return (bestTarget, targetPos);
        }

        /// <summary>
        ///     发射一轮抛物线子弹
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">抛物线终点位置</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        /// <param name="burstDelayList">子弹间隔列表</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, Vector3 shootBaseDir, float bulletQty, int anglesPropId, List<double> burstDelayList)
        {
            try
            {
                Debug.Log($"111111111 抛物线BurstOne开始 - Delay:{delay}s TrackPos:{trackPos} BulletQty:{bulletQty} AnglesPropId:{anglesPropId}");
                
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    //Debug.Log($"88888 抛物线BurstOne取消 - Token已取消");
                    return;
                }

                // 没有发射角度就不射击
                if (!SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesPropId, out CommonProp anglesProp))
                {
                    //Debug.Log($"88888 抛物线BurstOne失败 - 找不到角度配置 AnglesPropId:{anglesPropId}");
                    return;
                }

                Debug.Log($"111111111 抛物线BurstOne角度配置 - AnglesPropId:{anglesPropId} AnglesCount:{anglesProp.DoubleValues?.Count} Angles:{string.Join(",", anglesProp.DoubleValues?.ToList() ?? new List<double>())}");

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });

                List<float> angles = anglesProp.DoubleValues
                    .Select(x => (float)x).ToList();

                for (int z = 0; z < bulletQty; z++)
                {
                    if (token.IsCancellationRequested)
                    {
                        //Debug.Log($"88888 抛物线BurstOne中断 - 第{z}颗子弹创建时Token已取消");
                        return;
                    }

                    float angle = angles.IndexOfOrFirstOrDefault(z);
                    
                    // 0度角飞行的方向始终是从角色当前坐标向X轴正方向（右边），Y是抛物线曲线方向
                    Vector3 bulletDir = shootBaseDir;
                    if (angle != 0)
                    {
                        bulletDir = shootBaseDir.RotateAround(Vector3.forward, angle);
                    }

                    // 计算当前角度子弹的目标点
                    Vector3? currentTrackPos = CalculateAngleTargetPosition(trackPos, angle);

                    // 创建抛物线子弹
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    Debug.Log($"111111111 抛物线子弹创建参数 - 第{z}颗 Angle:{angle} BulletDir:{bulletDir} ActorPos:{Actor.Position} OriginalTrackPos:{trackPos} CalculatedTrackPos:{currentTrackPos} Penetrate:{maxPenetrateTimes} Bounce:{maxBounceTimes} Separate:{maxSeparateTimes}");

                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, currentTrackPos, angle,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    if (bullet != null)
                    {
                        // 设置子弹的追踪位置
                        bullet.AttackBaseDirFollowThing = attackBaseDirFollowThing;
                        bullet.TrackPosition = currentTrackPos;
                        bullet.Position = Actor.Position;
                        bullet.MoveDirection_Straight.Value = bulletDir;

                        // 确保子弹继承枪的速度配置
                        float gunBulletSpeed = (float)Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                        if (gunBulletSpeed > 0)
                        {
                            // 将枪的BulletSpeed配置传递给子弹
                            bullet.GetOrAddProp(PropType.BulletSpeed).DoubleValues.Clear();
                            bullet.GetOrAddProp(PropType.BulletSpeed).DoubleValues.Add(gunBulletSpeed);
                            //Debug.Log($"88888 抛物线子弹速度设置 - 第{z}颗 BulletId:{bullet.GetHashCode()} Speed:{gunBulletSpeed}");
                        }

                        //Debug.Log($"88888 抛物线子弹创建成功 - 第{z}颗 BulletId:{bullet.GetHashCode()} BulletType:{bullet.GetTotalLong(PropType.BulletType).FirstOrDefault()} Position:{bullet.Position} TrackPos:{bullet.TrackPosition} Speed:{bullet.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault()}");

                        MessageBroker.Default.Publish(new BornBullet { Bullet = bullet });
                        
                        //Debug.Log($"88888 抛物线子弹发布BornBullet事件 - 第{z}颗 BulletId:{bullet.GetHashCode()} 同时发射无延时");
                    }
                    else
                    {
                        //Debug.LogError($"88888 抛物线子弹创建失败 - 第{z}颗 CreateBullet返回null");
                    }
                }
                
                //Debug.Log($"88888 抛物线BurstOne完成 - 同时发射了{bulletQty}颗子弹");
            }
            catch (OperationCanceledException)
            {
                //Debug.Log($"88888 抛物线BurstOne操作取消");
                throw;
            }
            catch (MissingReferenceException ex)
            {
                //Debug.LogError($"88888 抛物线BurstOne引用丢失 - {ex.Message}");
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 抛物线BurstOne异常 - {ex.Message} StackTrace:{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 计算角度目标位置
        /// </summary>
        /// <param name="baseTrackPos">0度角的目标位置</param>
        /// <param name="angle">角度</param>
        /// <returns>计算后的目标位置</returns>
        private Vector3? CalculateAngleTargetPosition(Vector3? baseTrackPos, float angle)
        {
            if (!baseTrackPos.HasValue)
            {
                //Debug.Log($"99999 角度目标计算 - baseTrackPos为空，返回null");
                return null;
            }

            // 0度角直接返回原目标位置
            if (Mathf.Abs(angle) < 0.01f)
            {
                //Debug.Log($"99999 角度目标计算 - Angle:{angle} 是0度角，直接返回原目标位置:{baseTrackPos.Value}");
                return baseTrackPos;
            }

            Vector3 actorPos = Actor.Position;
            Vector3 zeroAngleTarget = baseTrackPos.Value;
            
            // 计算0度角坐标点到技能发射者的距离（作为半径）
            float radius = Vector3.Distance(actorPos, zeroAngleTarget);
            
            // 将角度转换为弧度
            float angleRad = angle * Mathf.Deg2Rad;
            
            // 以技能发射者为圆心，半径为上述距离，计算该角度对应的圆弧上的点
            // 0度角是向X轴正方向（右边），角度逆时针增加，Y是抛物线曲线方向
            Vector3 direction = new Vector3(Mathf.Cos(angleRad), Mathf.Sin(angleRad), 0f);
            Vector3 targetPos = actorPos + direction * radius;
            
            //Debug.Log($"99999 角度目标计算 - Angle:{angle}° ActorPos:{actorPos} ZeroAngleTarget:{zeroAngleTarget} Radius:{radius:F2} Direction:{direction} CalculatedTarget:{targetPos}");
            
            return targetPos;
        }
    }
} 