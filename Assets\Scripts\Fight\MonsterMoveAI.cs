using System.Collections;
using System.Collections.Generic;
using System.Linq;

using Cysharp.Threading.Tasks;

using DG.Tweening;

using Thing;

using UnityEngine;
using UnityEngine.Events;
using UnityEngine.InputSystem.HID;

using View;

using X.PB;

public class MonsterMoveAI : MonoBehaviour
{
    /// <summary>
    /// 寻路组件
    /// </summary>
    public NavAgent NavAgent { get; set; }

    public enum MoveState
    {
        None = 0,
        /// <summary>
        /// 追人
        /// </summary>
        Chase = 1,
        /// <summary>
        /// 闲逛
        /// </summary>
        Wander = 2,
        /// <summary>
        /// 冲撞
        /// </summary>
        Dash = 3,
        /// <summary>
        /// 跳跃
        /// </summary>
        Jump = 4,
        /// <summary>
        /// 现身
        /// </summary>
        Appear = 7,
    }

    private bool isMoving = true;
    private MonsterBase monster;
    private float moveSpeed;
    private double gunRange;
    private MoveState moveState;
    private float timerCounter1 = 0;
    // 用于控制move01动画播放频率的变量
    private float _lastMoveAnimTime = 0f;
    private float _moveAnimMinInterval = 2.5f; // 控制move01动画调用的最小间隔时间
    // 标记MoveType==10的怪物是否已经停止移动
    private bool hasStoppedMovingType10 = false;
    private IEnumerator PrintSpeed(float stopTime)
    {
        // 安全检查
        if (monster == null || !isMoving)
        {
            yield break;
        }
        
        //Debug.Log($"111111协程开始，计时器初始值={timerCounter1}，目标停止时间={stopTime}，初始移动速度={moveSpeed}，初始冲刺速度={dashSpeed}");
        float startTime = Time.time; // 记录协程开始的时间
        float elapsedTime = 0;
        
        while (elapsedTime < stopTime)
        {
            // 如果monster被销毁，停止协程
            if (monster == null || !isMoving)
            {
                yield break;
            }
            
            elapsedTime = Time.time - startTime; // 使用实际经过的时间而不是timerCounter1
            Vector2 currentVelocity = moveDir * moveSpeed * Time.deltaTime;
            //Debug.Log($"111111当前移动速度: {moveSpeed}，实际每帧移动距离: {currentVelocity.magnitude}，当前冲刺速度: {dashSpeed}，当前计时器值: {timerCounter1}，协程内计时: {elapsedTime}/{stopTime}");
            yield return new WaitForSeconds(0.5f);
        }
        
        // 确保在协程结束时正确设置状态
        moveSpeed = 0;
        dashSpeed = 0; // 同时将dashSpeed设为0，确保移动停止
        moveState = MoveState.None; // 重置移动状态
        
        // 如果是MoveType==10的怪物，标记为已停止
        if (monster && monster.MonsterThing != null && monster.MonsterThing.CsvRow_BattleBrushEnemy != null && monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 10)
        {
            hasStoppedMovingType10 = true;
            //Debug.Log($"111111协程内移动结束，速度已设置为0，计时器值: {timerCounter1}，协程内计时: {elapsedTime}，已永久停止移动");
        }
        else
        {
            //Debug.Log($"111111协程内移动结束，速度已设置为0，计时器值: {timerCounter1}，协程内计时: {elapsedTime}");
        }
    }
    private float timerCounter2 = 0;

    private float chaseTime = 5;
    private float wanderTime = 3;
    private float idleTime = 2;
    private Vector2 moveDir;
    private float[] moveParams;
    // 为MoveType=9的怪物添加独立的角度变量
    private float monsterAngle = 0;
    private float dashSpeed = 20;
    private Vector2 targetPos;
    private Vector2 lastAppearPos;
    private float disappearTime;
    private float dashTime;
    /// <summary>
    /// 前摇时间
    /// </summary>
    private float precastTime;
    private EffectUnit warningEffect = null;

    private void OnEnable()
    {
        monster = GetComponent<MonsterBase>();
        moveState = MoveState.None;
        if (NavAgent) NavAgent.StopNavMove();
        monster?.PlayAnimation("idle01", true);
        
        // 安全检查
        if (monster == null || monster.Thing == null || monster.MonsterThing == null)
        {
            isMoving = false;
            return;
        }
        
        // 初始化速度和枪械
        moveSpeed = (float)monster.Thing.GetTotalDouble(PropType.Speed).FirstOrDefault();
        
        // 安全检查枪
        if (monster.MonsterThing.Guns != null && monster.MonsterThing.Guns.Value != null)
        {
            gunRange = monster.MonsterThing.Guns.Value.GetTotalDouble(PropType.GunRange).FirstOrDefault();
        }
        
        // 安全检查移动参数
        if (monster.MonsterThing.CsvRow_BattleBrushEnemy != null && 
            monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveParams != null &&
            monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveParams.Length > 0)
        {
            moveParams = monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveParams;
            Debug.Log($"[MoveType10调试] 表格配置移动方向参数={monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType}");
            Debug.Log($"[MoveType10调试] 移动参数类型: {monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType} 移动参数: {moveParams[0]} 配置方向: {(moveParams[0] == 1 ? "正方向" : "负方向")} 实际方向: {(moveDir.x > 0 ? "正方向" : "负方向")}");
            Debug.Log($"[MoveType10调试] MoveParams: {string.Join(", ", moveParams)}");
            Debug.Log($"[MoveType10调试] MoveParams数组长度: {moveParams.Length}");
            for (int i = 0; i < moveParams.Length; i++)
            {
                Debug.Log($"[MoveType10调试] moveParams[{i}] = {moveParams[i]} (类型: {moveParams[i].GetType()})");
            }
            
            // 为MoveType=9的怪物初始化独立的角度变量
            if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 9 && moveParams.Length > 1)
            {
                monsterAngle = moveParams[1]; // 从配置中获取初始角度值
                moveState = MoveState.Dash; // 设置为移动状态，确保开始移动
                //Debug.Log($"MoveType=9怪物初始化独立角度: {monsterAngle}，设置为移动状态");
            }
            // 确保moveParams=1为正方向，2为负方向
            if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 10)
            {
                Debug.Log($"2222222222 [MoveType10-OnEnable] 怪物 {monster.name} 开始初始化，MoveType={monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType}");

                // 对于MoveType==10，直接根据moveParams[0]设置方向
                // moveParams[0]表示方向：1=向上，2=向下，3=向左，4=向右
                int direction = Mathf.RoundToInt(moveParams[0]); // 转换为整数避免浮点数精度问题
                Debug.Log($"2222222222 [MoveType10-OnEnable] 原始方向参数={moveParams[0]}, 转换后方向={direction}");

                switch(direction)
                {
                    case 1:
                        moveDir = new Vector2(0, 1); // 向上移动（正方向）
                        Debug.Log($"2222222222 [MoveType10-OnEnable] 设置向上移动方向: {moveDir}");
                        break;
                    case 2:
                        moveDir = new Vector2(0, -1); // 向下移动（负方向）
                        Debug.Log($"2222222222 [MoveType10-OnEnable] 设置向下移动方向: {moveDir}");
                        break;
                    case 3:
                        moveDir = new Vector2(-1, 0); // 向左移动
                        Debug.Log($"2222222222 [MoveType10-OnEnable] 设置向左移动方向: {moveDir}");
                        break;
                    case 4:
                        moveDir = new Vector2(1, 0); // 向右移动
                        Debug.Log($"2222222222 [MoveType10-OnEnable] 设置向右移动方向: {moveDir}");
                        break;
                    default:
                        moveDir = new Vector2(0, 1); // 默认向上移动
                        Debug.Log($"2222222222 [MoveType10-OnEnable] 使用默认向上移动方向: {moveDir}");
                        break;
                }

                Debug.Log($"2222222222 [MoveType10-OnEnable] 设置初始移动方向={moveDir}，方向参数={moveParams[0]}，速度参数={moveParams[1]}，停留时间={moveParams[2]}");

                // 重置计时器、移动状态和停止标志
                timerCounter1 = 0;
                moveState = MoveState.None;
                hasStoppedMovingType10 = false; // 重置停止标志

                Debug.Log($"2222222222 [MoveType10-OnEnable] 怪物 {monster.name} 初始化完成 - moveDir:{moveDir}, moveState:{moveState}, hasStoppedMovingType10:{hasStoppedMovingType10}");
            }
            else
            {
                // 其他MoveType的处理保持不变
                if(moveParams[0] == 2) moveDir = new Vector2(-Mathf.Abs(moveDir.x), -Mathf.Abs(moveDir.y));
                else if(moveParams[0] == 1) moveDir = new Vector2(Mathf.Abs(moveDir.x), Mathf.Abs(moveDir.y));
            }
        }
        else
        {
            // 默认移动参数
            moveParams = new float[] { 1 };
            moveDir = new Vector2(1, 0);
        }
        
        lastAppearPos = transform.position;
    }

    /// <summary>
    /// 尝试播放移动动画，但控制调用频率
    /// </summary>
    private void TryPlayMoveAnimation()
    {
        if (monster == null) return;
        
        // 检查距离上次播放动画的时间间隔
        float timeSinceLastAnim = Time.time - _lastMoveAnimTime;
        if (timeSinceLastAnim >= _moveAnimMinInterval)
        {
            // 时间间隔足够，可以播放动画
            _lastMoveAnimTime = Time.time;
            //Debug.Log($"1111111 MonsterMoveAI 调用move01动画: 当前时间={Time.time:F2}, 距上次={timeSinceLastAnim:F2}秒");
            monster.PlayAnimation("move01", true);
        }
        else
        {
            // 时间间隔不够，跳过此次播放
            //Debug.Log($"1111111 MonsterMoveAI 跳过move01动画调用: 当前时间={Time.time:F2}, 距上次仅={timeSinceLastAnim:F2}秒 < 所需{_moveAnimMinInterval:F2}秒");
        }
    }
    
    private void Update()
    {
        if (!isMoving) return;
        if (!NavAgent || monster == null || monster.MonsterThing == null ) return;
        
        // 安全检查BattleMgr和Actor
        if (SingletonMgr.Instance == null || SingletonMgr.Instance.BattleMgr == null || SingletonMgr.Instance.BattleMgr.Actor == null)
        {
            return;
        }
        
        Vector2 currentPos = transform.position;
        
        // 只有非MoveType==10和非MoveType==9的怪物才需要获取玩家位置并设置移动方向
        if (monster.MonsterThing.CsvRow_BattleBrushEnemy != null && 
            monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType != 10 && 
            monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType != 9)
        {
            Vector2 targetPos = SingletonMgr.Instance.BattleMgr.Actor.Position;
            moveDir = targetPos - currentPos;
            //Debug.Log($"当前位置: {currentPos}, 目标位置: {targetPos}");
            //Debug.Log($"当前移动方向: {moveDir}");
            //Debug.Log($"当前移动速度: {moveSpeed}");
        }
        else if (monster.MonsterThing.CsvRow_BattleBrushEnemy != null && 
                 monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 10)
        {
            // MoveType==10的怪物保持原有方向，不追击玩家
            Debug.Log($"[MoveType10调试] 当前位置: {currentPos}, 保持原有方向: {moveDir}");
            Debug.Log($"[MoveType10调试] 当前移动速度: {moveSpeed}");
        }
        
        // 安全检查怪物配置
        if (monster.MonsterThing.CsvRow_BattleBrushEnemy == null || moveParams == null || moveParams.Length == 0) 
        {
            return;
        }
        
        if (monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 1)
        {
            // 确保moveParams有足够的元素
            if (moveParams.Length >= 3)
            {
                wanderTime = moveParams[0];//闲逛时间
                chaseTime = moveParams[1];//追人时间
                idleTime = moveParams[2];//停留时间
            }
            else
            {
                // 默认值
                wanderTime = 3;
                chaseTime = 5;
                idleTime = 2;
            }
            
            timerCounter1 += Time.deltaTime;
            if (moveState == MoveState.None)
            {
                if (timerCounter1 >= idleTime)
                {
                    timerCounter1 = 0;
                    moveState = MoveState.Wander;
                    NavAgent.StopNavMove();
                    targetPos = GetRandomWalkablePosByRadius(transform.position, 15);
                    //Debug.Log($"进入闲逛状态，目标位置: {targetPos}");
                    AINavMove(targetPos, moveSpeed, () => { monster?.PlayAnimation("idle01", true); });
                }
            }
            else if (moveState == MoveState.Wander)
            {
                if (timerCounter1 >= wanderTime)
                {
                    timerCounter1 = 0;
                    moveState = MoveState.Chase;
                    NavAgent.StopNavMove();
                    Chase();
                }
            }
            else if (moveState == MoveState.Chase)
            {
                if (timerCounter1 >= chaseTime)
                {
                    timerCounter1 = 0;
                    moveState = MoveState.None;
                    NavAgent.StopNavMove();
                    monster?.PlayAnimation("idle01", true);
                }
            }
        }
        else if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 8)
        {
            // 从出生点垂直向下移动
            moveDir = new Vector2(0, -1); // 向下移动
            transform.Translate(moveDir * Time.deltaTime * moveSpeed);
            
            // 确保移动时播放移动动画，但控制调用频率
            TryPlayMoveAnimation();
            
            // 检测是否到达屏幕边界 - 根据相机旋转角度动态判断
            Vector3 screenPosCheck = Camera.main.WorldToViewportPoint(transform.position);
            bool shouldSuicide = false;
            
            // 根据相机的Z轴旋转角度判断哪个边界是"下边界"
            float cameraZRotation = Camera.main.transform.eulerAngles.z;
            
            if (Mathf.Abs(cameraZRotation) < 45f || Mathf.Abs(cameraZRotation - 360f) < 45f)
            {
                // 相机未旋转或接近0度，检测下边界 (y < 0)
                shouldSuicide = screenPosCheck.y < 0;
            }
            else if (Mathf.Abs(cameraZRotation - 90f) < 45f)
            {
                // 相机旋转约90度，原下边界变成左边界，检测左边界 (x < 0)
                shouldSuicide = screenPosCheck.x < 0;
            }
            else if (Mathf.Abs(cameraZRotation - 180f) < 45f)
            {
                // 相机旋转约180度，原下边界变成上边界，检测上边界 (y > 1)
                shouldSuicide = screenPosCheck.y > 1;
            }
            else if (Mathf.Abs(cameraZRotation - 270f) < 45f)
            {
                // 相机旋转约270度，原下边界变成右边界，检测右边界 (x > 1)
                shouldSuicide = screenPosCheck.x > 1;
            }
            
            if (shouldSuicide)
            {
                // 到达对应边界，怪物自杀
                monster.MonsterThing.DeathCause = Thing.MonsterDeathCause.BoundarySuicide;
                monster.MonsterThing.Hp.Value = 0;
                //Debug.Log("怪物到达屏幕边界，已自杀");
            }
        }
        else if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 9)
        {
            // 根据moveParams[1]的值作为与垂直方向的夹角来控制移动方向
            float angle = monsterAngle; // 使用实例独立的角度变量
            float radians = angle * Mathf.Deg2Rad;
            moveDir = new Vector2(Mathf.Sin(radians), -Mathf.Cos(radians));
            
            // 屏幕边界检测
            Vector3 screenPosCheck = Camera.main.WorldToViewportPoint(transform.position);
            if (screenPosCheck.x < 0 || screenPosCheck.x > 1 || screenPosCheck.y < 0 || screenPosCheck.y > 1)
            {
                // 碰到边界后反转角度，但只影响当前实例
                monsterAngle = -monsterAngle; // 反转当前实例的角度
                radians = monsterAngle * Mathf.Deg2Rad;
                moveDir = new Vector2(Mathf.Sin(radians), -Mathf.Cos(radians));
                
                // 根据相机旋转角度判断是否到达"下边界"进行自杀
                bool shouldSuicide = false;
                float cameraZRotation = Camera.main.transform.eulerAngles.z;
                
                if (Mathf.Abs(cameraZRotation) < 45f || Mathf.Abs(cameraZRotation - 360f) < 45f)
                {
                    // 相机未旋转或接近0度，检测下边界 (y < 0)
                    shouldSuicide = screenPosCheck.y < 0;
                }
                else if (Mathf.Abs(cameraZRotation - 90f) < 45f)
                {
                    // 相机旋转约90度，原下边界变成左边界，检测左边界 (x < 0)
                    shouldSuicide = screenPosCheck.x < 0;
                }
                else if (Mathf.Abs(cameraZRotation - 180f) < 45f)
                {
                    // 相机旋转约180度，原下边界变成上边界，检测上边界 (y > 1)
                    shouldSuicide = screenPosCheck.y > 1;
                }
                else if (Mathf.Abs(cameraZRotation - 270f) < 45f)
                {
                    // 相机旋转约270度，原下边界变成右边界，检测右边界 (x > 1)
                    shouldSuicide = screenPosCheck.x > 1;
                }
                
                if (shouldSuicide)
                {
                    monster.MonsterThing.DeathCause = Thing.MonsterDeathCause.BoundarySuicide;
                    monster.MonsterThing.Hp.Value = 0;
                    //Debug.Log("怪物到达屏幕边界，已自杀");
                }
            }
            
            // 执行移动
            Vector3 beforeMove = transform.position;
            transform.Translate(moveDir * Time.deltaTime * moveSpeed);
            Vector3 afterMove = transform.position;
            float movedDistance = Vector3.Distance(beforeMove, afterMove);
            
            // 确保移动时播放移动动画，只有当怪物真正移动了且没有攻击动画时才播放移动动画
            if (movedDistance > 0.001f)
            {
                // 获取当前动画名称，如果不是attack01则播放move01
                string currentAnim = monster.GetComponent<Spine.Unity.SkeletonAnimation>()?.AnimationName;
                if (currentAnim != "attack01")
                {
                    TryPlayMoveAnimation();
                }
            }
        }
        else if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 10)
        {
            // MoveType 10的特殊处理
            float dashSpeed = moveParams[1]; // 移动速度 - 使用moveParams[1]作为速度
            float stopTime = moveParams[2]; // 停留时间
            Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} Update中 - 移动速度={dashSpeed}, 停留时间={stopTime}, 当前移动状态={moveState}, moveDir={moveDir}");

            // 如果怪物已经停止移动，则不再重新启动移动逻辑
            if (hasStoppedMovingType10)
            {
                Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 已永久停止移动，跳过Update逻辑");
                // 确保状态和速度保持为0
                moveSpeed = 0;
                dashSpeed = 0;
                moveState = MoveState.None;
                return; // 直接返回，不执行后续逻辑
            }
            
            // 检测是否到达屏幕边界 - 根据相机旋转角度动态判断
            Vector3 screenPosCheck = Camera.main.WorldToViewportPoint(transform.position);
            bool shouldSuicide = false;
            
            // 根据相机的Z轴旋转角度判断哪个边界是"下边界"
            float cameraZRotation = Camera.main.transform.eulerAngles.z;
            
            if (Mathf.Abs(cameraZRotation) < 45f || Mathf.Abs(cameraZRotation - 360f) < 45f)
            {
                // 相机未旋转或接近0度，检测下边界 (y < 0)
                shouldSuicide = screenPosCheck.y < 0;
            }
            else if (Mathf.Abs(cameraZRotation - 90f) < 45f)
            {
                // 相机旋转约90度，原下边界变成左边界，检测左边界 (x < 0)
                shouldSuicide = screenPosCheck.x < 0;
            }
            else if (Mathf.Abs(cameraZRotation - 180f) < 45f)
            {
                // 相机旋转约180度，原下边界变成上边界，检测上边界 (y > 1)
                shouldSuicide = screenPosCheck.y > 1;
            }
            else if (Mathf.Abs(cameraZRotation - 270f) < 45f)
            {
                // 相机旋转约270度，原下边界变成右边界，检测右边界 (x > 1)
                shouldSuicide = screenPosCheck.x > 1;
            }
            
            if (shouldSuicide)
            {
                // 到达对应边界，怪物自杀
                monster.MonsterThing.DeathCause = Thing.MonsterDeathCause.BoundarySuicide;
                monster.MonsterThing.Hp.Value = 0;
                //Debug.Log("怪物到达屏幕边界，已自杀");
                return;
            }
            
            if (moveState == MoveState.None && !hasStoppedMovingType10)
            {
                Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 开始初始化移动状态");
                // 初始化移动状态
                moveSpeed = dashSpeed; // 设置移动速度
                // 确保moveDir已经在OnEnable中根据moveParams[0]设置好了
                Vector2 actualMovement = moveDir * moveSpeed * Time.deltaTime;
                Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 开始移动，速度={moveSpeed}，实际每帧移动距离={actualMovement.magnitude}，方向={moveDir}，停留时间={stopTime}");
                Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 移动参数检查 - moveParams[0]={moveParams[0]}, moveParams[1]={moveParams[1]}, moveParams[2]={moveParams[2]}");
                monster?.PlayAnimation("attack01");
                moveState = MoveState.Dash;
                timerCounter1 = 0;
                StopAllCoroutines(); // 停止所有协程，避免多个计时器同时运行
                StartCoroutine(PrintSpeed(stopTime));
                Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 移动状态设置为Dash，开始计时器");
            }
            
            if (moveState == MoveState.Dash)
            {
                timerCounter1 += Time.deltaTime;
                Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} Dash状态 - 当前计时器值={timerCounter1}，停止时间={stopTime}");

                if (timerCounter1 < stopTime)
                {
                    // 只有在计时器未到达停止时间时才移动
                    // 检查是否碰到边界
                    Vector3 screenPos2 = Camera.main.WorldToViewportPoint(transform.position);
                    bool hitBoundary = screenPos2.x < 0 || screenPos2.x > 1 || screenPos2.y < 0 || screenPos2.y > 1;

                    // 检查是否碰到地形
                    RaycastHit2D hit2D = Physics2D.Raycast(transform.position, moveDir, moveSpeed * Time.deltaTime, 1 << LayerMask.NameToLayer("Terrain"));

                    Vector2 actualMovement = moveDir * moveSpeed * Time.deltaTime;

                    Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 移动检查 - moveDir={moveDir}, moveSpeed={moveSpeed}, actualMovement={actualMovement}, hitBoundary={hitBoundary}, hit2D={hit2D.collider != null}");

                    if (hit2D.collider == null && !hitBoundary)
                    {
                        // 正常移动
                        Vector2 oldPos = transform.position;
                        transform.position = (Vector2)transform.position + actualMovement;
                        Vector2 newPos = transform.position;
                        Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 正在移动 - 旧位置={oldPos}, 新位置={newPos}, 移动距离={Vector2.Distance(oldPos, newPos)}");
                        // 移动中播放移动动画
                        monster?.PlayAnimation("move01", true);
                    }
                    else
                    {
                        // 碰到障碍物或边界，反转方向
                        Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 碰到障碍物或边界，当前方向={moveDir}，反转方向");
                        moveDir = -moveDir; // 方向变负
                        Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 方向反转完成，新方向={moveDir}");
                    }
                }
                else
                {
                    // 停留时间到，停止移动
                    Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 停留时间到，停止移动，计时器值={timerCounter1}");
                    moveSpeed = 0;
                    dashSpeed = 0; // 确保dashSpeed也设为0
                    moveState = MoveState.None;
                    // 标记为已停止，确保不会重新开始移动
                    hasStoppedMovingType10 = true;
                    Debug.Log($"2222222222 [MoveType10-Update] 怪物 {monster.name} 已永久停止移动");
                    // 停止移动后播放待机动画
                    monster?.PlayAnimation("idle01", true);
                }
            }
        }
        else if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 1)
        {
            // 从屏幕上方垂直向下移动
            moveDir = new Vector2(0, -1); // 向下移动
            transform.Translate(moveDir * Time.deltaTime * moveSpeed);
        }
        else if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 3)
        {
            gunRange = moveParams[0];//冲撞触发距离
            dashSpeed = moveParams[1];//冲撞速度
            idleTime = moveParams[2];//冲撞完停留时间
            precastTime = moveParams[3];//预警时间
            float distance = Vector2.Distance(transform.position, SingletonMgr.Instance.BattleMgr.Actor.Position);
            if (moveState == MoveState.None)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= idleTime)
                {
                    timerCounter1 = 0;
                    targetPos = SingletonMgr.Instance.BattleMgr.Actor.Position;
                    moveDir = targetPos - (Vector2)transform.position;
                    RaycastHit2D hit2D = Physics2D.Raycast(transform.position, moveDir, moveDir.magnitude, 1 << LayerMask.NameToLayer("Terrain"));
                    if (distance > gunRange || hit2D.collider != null)
                    {
                        moveState = MoveState.Chase;
                        Chase();
                    }
                    else
                    {
                        moveState = MoveState.Dash;
                        monster?.PlayAnimation("attack01");
                    }
                }
            }

            if(moveState == MoveState.Chase)
            {
                if (distance <= gunRange)
                {
                    targetPos = SingletonMgr.Instance.BattleMgr.Actor.Position;
                    moveDir = targetPos - (Vector2)transform.position;
                    //Debug.Log($"冲撞触发距离: {distance}, 目标位置: {targetPos}");
                    RaycastHit2D hit2D = Physics2D.Raycast(transform.position, moveDir, moveDir.magnitude, 1 << LayerMask.NameToLayer("Terrain"));
                    if(hit2D.collider == null)
                    {
                        NavAgent.StopNavMove();
                        moveState = MoveState.Dash;
                        //Debug.Log($"进入冲撞状态，速度: {dashSpeed}");
                        monster?.PlayAnimation("attack01");
                    }
                    monster.SetSpineModelDirection(moveDir.x > 0 ? 1 : -1);
                }
                else
                {
                    Chase();
                }
            }

            if(moveState == MoveState.Dash)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter2 != -1)
                {
                    timerCounter2 = -1;
                    List<Vector2> points = new List<Vector2>();
                    float dis = 0;
                    points.Add(transform.position);
                    Vector2 origin = transform.position;
                    Vector2 dir = (targetPos - (Vector2)transform.position).normalized;
                    for (int i = 0; i < 10; i++)
                    {
                        RaycastHit2D hit2d = Physics2D.Raycast(origin, dir, 100, 1 << LayerMask.NameToLayer("Terrain"));
                        dis += hit2d.distance;
                        points.Add(hit2d.point);
                        if (dis >= dashSpeed * dashTime)
                        {
                            break;
                        }
                        else
                        {
                            dir = Vector2.Reflect(dir, hit2d.normal).normalized;
                            origin = hit2d.point + dir * 0.1f;
                        }
                    }
                    EffectMgr.Instance.ShowEffect(EffectPath.WarningPathComp, transform.position, 1, null, 1f + precastTime, 5, (obj) =>
                    {
                        obj.GetComponent<WarningPathComp>().SetCurve(points);
                        warningEffect = obj.GetComponent<EffectUnit>();
                    }).Forget();
                }

                if (timerCounter1 >= precastTime)
                {
                    warningEffect?.Recycle();
                    warningEffect = null;
                    RaycastHit2D hit2D = Physics2D.Raycast(transform.position, moveDir, dashSpeed * Time.deltaTime + 0.1f, 1 << LayerMask.NameToLayer("Terrain"));
                    if (hit2D.collider == null)
                    {
                        transform.position = (Vector2)transform.position + moveDir.normalized * dashSpeed * Time.deltaTime;
                        // MoveType 9冲撞型移动时播放攻击动画，不需要修改
                    }
                    else
                    {
                        moveDir = -moveDir;
                    }

                    if (Vector2.Distance(transform.position, targetPos) <= dashSpeed * Time.deltaTime)
                    {
                        moveState = MoveState.None;
                        timerCounter2 = 0;
                        timerCounter1 = 0;
                    }
                }
            }
        }
        else if (monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 4)
        {
            gunRange = moveParams[0];//跳跃触发距离
            idleTime = moveParams[1];//跳跃完停留时间
            precastTime = moveParams[2];//预警前摇时间
            float distance = Vector2.Distance(transform.position, SingletonMgr.Instance.BattleMgr.Actor.Position);
            if (moveState == MoveState.None)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= idleTime)
                {
                    timerCounter1 = 0;
                    targetPos = SingletonMgr.Instance.BattleMgr.Actor.Position;
                    if (distance > gunRange)
                    {
                        moveState = MoveState.Chase;
                        Chase();
                    }
                    else
                    {
                        NavAgent.StopNavMove();
                        moveState = MoveState.Jump;
                        EffectMgr.Instance.ShowEffect(EffectPath.WarningCircleComp, targetPos, 1, null, 1 + precastTime, 10, (obj) =>
                        {
                            obj.GetComponent<WarningCircleComp>().SetCircle(1, 4);
                        }).Forget();
                        StartCoroutine(DelayedJumpAttack(targetPos, precastTime));
                    }
                }
            }
        }
    }

    private IEnumerator DelayedJumpAttack(Vector2 targetPos, float precastTime)
    {
        yield return new WaitForSeconds(precastTime);
        monster?.PlayAnimation("attack01");
        moveDir = (Vector2)transform.position - targetPos;
        monster.SetSpineModelDirection(moveDir.x > 0 ? 1 : -1);
        transform.DOJump(targetPos, 10, 1, 0.65f).SetDelay(0).onComplete = () => { moveState = MoveState.None; };
    }

    private IEnumerator DelayedJumpAttackWithEffect(Vector2 targetPos, float precastTime)
    {
        yield return new WaitForSeconds(precastTime);
        monster?.PlayAnimation("attack01");
        transform.DOJump(targetPos, 10, 1, 0.65f).SetDelay(0).onComplete = () =>
        {
            moveState = MoveState.None;
            warningEffect?.Recycle();
            warningEffect = null;
        };
    }

    private IEnumerator DelayedJumpAttackWithEffectRandom(Vector2 targetPos, float precastTime)
    {
        yield return new WaitForSeconds(precastTime);
        monster?.PlayAnimation("attack01");
        moveDir = (Vector2)transform.position - targetPos;
        monster.SetSpineModelDirection(moveDir.x > 0 ? 1 : -1);
        transform.DOJump(targetPos, 10, 1, 0.65f).SetDelay(0).onComplete = () =>
        {
            moveState = MoveState.None;
            warningEffect?.Recycle();
            warningEffect  = null;
        };
    }

            if (moveState == MoveState.Chase)
            {
                if (distance <= gunRange)
                {
                    NavAgent.StopNavMove();
                    targetPos = SingletonMgr.Instance.BattleMgr.Actor.Position;
                    moveState = MoveState.Jump;
                    EffectMgr.Instance.ShowEffect(EffectPath.WarningCircleComp, targetPos, 1, null, 1 + precastTime, 10, (obj) =>
                    {
                        obj.GetComponent<WarningCircleComp>().SetCircle(1, 4);
                        warningEffect = obj.GetComponent<EffectUnit>();
                    }).Forget();
                    StartCoroutine(DelayedJumpAttackWithEffect(targetPos, precastTime));
                }
                else
                {
                    Chase();
                }
            }
        }
        else if (monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 5)
        {
            gunRange = moveParams[0];//跳跃触发距离
            idleTime = moveParams[1];//跳跃完停留时间
            precastTime = moveParams[2];//预警前摇时间
            float distance = Vector2.Distance(transform.position, SingletonMgr.Instance.BattleMgr.Actor.Position);
            if (moveState == MoveState.None)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= idleTime)
                {
                    timerCounter1 = 0;
                    targetPos = GetRandomWalkablePosByRadius(transform.position, (int)gunRange);
                    NavAgent.StopNavMove();
                    moveState = MoveState.Jump;
                    EffectMgr.Instance.ShowEffect(EffectPath.WarningCircleComp, targetPos, 1, null, 1 + precastTime, 10, (obj) =>
                    {
                        obj.GetComponent<WarningCircleComp>().SetCircle(1, 4);
                        warningEffect = obj.GetComponent<EffectUnit>();
                    }).Forget();
                    StartCoroutine(DelayedJumpAttackWithEffectRandom(targetPos, precastTime));
                }
            }
        }
        else if (monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 6)
        {
            gunRange = moveParams[0];//现身点半径
            idleTime = moveParams[1];//现身完停留时间
            disappearTime = moveParams[2];//隐身时间
            //float distance = Vector2.Distance(transform.position, SingletonMgr.Instance.BattleMgr.Actor.Position);
            if (moveState == MoveState.None)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= disappearTime)
                {
                    timerCounter1 = 0;
                    moveState = MoveState.Appear;
                    targetPos = GetRandomWalkablePosByRadius(lastAppearPos,12);
                    transform.position = targetPos;
                    lastAppearPos = targetPos;
                    transform.DOScale(Vector3.one, 0.5f);
                }
            }

            if (moveState == MoveState.Appear)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= idleTime)
                {
                    timerCounter1 = 0;
                    moveState = MoveState.None;
                    transform.DOScale(Vector3.one * 0.6f, 0.5f).onComplete = () => 
                    { 
                        transform.position = lastAppearPos + Vector2.left * 2000; 
                    };
                }
            }
        }
        else if(monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 7)
        {
            dashTime = moveParams[0];//冲撞时间
            dashSpeed = moveParams[1];//冲撞速度
            idleTime = moveParams[2];//冲撞完停留时间
            precastTime = moveParams[3];
            if (moveState == MoveState.None)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= idleTime)
                {
                    timerCounter1 = 0;
                    targetPos = SingletonMgr.Instance.BattleMgr.Actor.Position;
                    moveDir = (targetPos - (Vector2)transform.position).normalized;
                    moveState = MoveState.Dash;
                    monster?.PlayAnimation("attack01");
                    monster.SetSpineModelDirection(moveDir.x > 0 ? 1 : -1);
                }
            }

            if (moveState == MoveState.Dash)
            {
                timerCounter1 += Time.deltaTime;

                if (timerCounter2 != -1)
                {
                    timerCounter2 = -1;
                    List<Vector2> points = new List<Vector2>();
                    float dis = 0;
                    points.Add(transform.position);
                    Vector2 origin = transform.position;
                    Vector2 dir = (targetPos - (Vector2)transform.position).normalized;
                    for (int i = 0; i < 10; i++)
                    {
                        RaycastHit2D hit2d = Physics2D.Raycast(origin, dir, 100, 1 << LayerMask.NameToLayer("Terrain"));
                        dis += hit2d.distance;
                        points.Add(hit2d.point);
                        if (dis >= dashSpeed * dashTime)
                        {
                            break;
                        }
                        else
                        {
                            dir = Vector2.Reflect(dir, hit2d.normal).normalized;
                            origin = hit2d.point + dir * 0.1f;
                        }
                    }
                    EffectMgr.Instance.ShowEffect(EffectPath.WarningPathComp, transform.position, 1, null, 1f + precastTime, 5, (obj) =>
                    {
                        obj.GetComponent<WarningPathComp>().SetCurve(points);
                        warningEffect = obj.GetComponent<EffectUnit>();
                    }).Forget();
                }

                if (timerCounter1 >= precastTime)
                {
                    warningEffect?.Recycle();
                    warningEffect = null;
                    RaycastHit2D hit2D = Physics2D.Raycast(transform.position, moveDir, dashSpeed * Time.deltaTime, 1 << LayerMask.NameToLayer("Terrain"));
                    if (hit2D.collider == null)
                    {
                        transform.position = (Vector2)transform.position + moveDir * dashSpeed * Time.deltaTime;
                        // MoveType 9冲撞型移动时播放攻击动画，不需要修改
                    }
                    else
                    {
                        moveDir = -moveDir;
                    }

                    if (timerCounter1 >= dashTime + precastTime)
                    {
                        timerCounter1 = 0;
                        timerCounter2 = 0;
                        moveState = MoveState.None;
                    }
                }
            }
        }
        else if (monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 8)
        {
            dashSpeed = moveParams[0]; // 移动速度
            // moveParams[1] == 1: 向下, == 2: 向上
            float stopTime = moveParams[2]; // 停留时间
            if (moveState == MoveState.None)
            {
                if (moveParams[1] == 1)
                    moveDir = new Vector2(0, -1); // 向下
                else if (moveParams[1] == 2)
                    moveDir = new Vector2(0, 1); // 向上
                else
                    moveDir = Vector2.zero;
                moveSpeed = dashSpeed; // 设置移动速度
                ////Debug.Log($"MoveType 10: 111111移动速度={dashSpeed}, 111111移动方向={moveDir}, 111111停留时间={stopTime}");
                monster?.PlayAnimation("attack01");
                moveState = MoveState.Dash;
                timerCounter1 = 0;
                StartCoroutine(PrintSpeed(stopTime));
            }
            if (moveState == MoveState.Dash)
            {
                timerCounter1 += Time.deltaTime;
                //Debug.Log($"111111MoveType 10: 当前计时器值={timerCounter1}, 停止时间={stopTime}");
                if (timerCounter1 < stopTime)
                {
                    // 只有在计时器未到达停止时间时才移动
                    float currentDashSpeed = dashSpeed;
                    //Debug.Log($"111111MoveType 10: 当前位置={transform.position}, 目标位置={(Vector2)transform.position + moveDir * currentDashSpeed * Time.deltaTime}, 当前速度={currentDashSpeed}");
                    RaycastHit2D hit2D = Physics2D.Raycast(transform.position, moveDir, currentDashSpeed * Time.deltaTime, 1 << LayerMask.NameToLayer("Terrain"));
                    if (hit2D.collider == null)
                    {
                        transform.position = (Vector2)transform.position + moveDir * currentDashSpeed * Time.deltaTime;
                        // 移动中播放移动动画
                        monster?.PlayAnimation("move01", true);
                    }
                    else
                    {
                        // 碰到障碍物后停止移动
                        moveSpeed = 0;
                        dashSpeed = 0;
                        moveState = MoveState.None;
                        // 停止移动后播放待机动画
                        monster?.PlayAnimation("idle01", true);
                    }
                }
                else
                {
                    // 停留时间到，停止移动
                    moveSpeed = 0;
                    dashSpeed = 0; // 确保dashSpeed也设为0
                    moveState = MoveState.None;
                    //Debug.Log($"111111MoveType 10: 停留时间到，停止移动，计时器值={timerCounter1}");
                    // 停止移动后播放待机动画
                    monster?.PlayAnimation("idle01", true);
                }
            }
        }
        else if (monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 11)
        {
            gunRange = moveParams[0];//现身点半径
            idleTime = moveParams[1];//现身完停留时间
            disappearTime = moveParams[2];//隐身时间
            //float distance = Vector2.Distance(transform.position, SingletonMgr.Instance.BattleMgr.Actor.Position);
            if (moveState == MoveState.None)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= disappearTime)
                {
                    timerCounter1 = 0;
                    moveState = MoveState.Appear;
                    targetPos = GetRandomWalkablePosByRadius(monster.MonsterThing.Position,12);
                    transform.position = targetPos;
                    lastAppearPos = targetPos;
                    transform.DOScale(Vector3.one, 0.5f);
                }
            }

            if (moveState == MoveState.Appear)
            {
                timerCounter1 += Time.deltaTime;
                if (timerCounter1 >= idleTime)
                {
                    timerCounter1 = 0;
                    moveState = MoveState.None;
                    transform.DOScale(Vector3.one * 0.6f, 0.5f).onComplete = () => 
                    {
                        transform.position = lastAppearPos + Vector2.left * 2000; 
                    };
                }
            }
        }
        
        if (NavAgent.IsNaving && monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType != 10)
        {
            Debug.Log($"2222222222 [NavAgent] 怪物 {monster.name} NavAgent控制移动 - MoveType={monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType}, NavAgent.MoveDir={NavAgent.MoveDir}");
            moveDir = -NavAgent.MoveDir;
            monster.SetSpineModelDirection(moveDir.x > 0 ? 1 : -1);
            // 移动中播放移动动画
            monster?.PlayAnimation("move01", true);
        }
        else if (NavAgent.IsNaving && monster.MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 10)
        {
            Debug.Log($"2222222222 [NavAgent] 怪物 {monster.name} MoveType=10被排除，不受NavAgent干扰 - NavAgent.IsNaving={NavAgent.IsNaving}");
        }
        else if (moveState == MoveState.None && moveSpeed <= float.Epsilon && dashSpeed <= float.Epsilon)
        {
            // 非移动状态播放待机动画
            monster?.PlayAnimation("idle01", true);
        }
        //monster.SetSpineModelDirection((moveDir).x > 0 ? 1 : -1);
    }

    /// <summary>
    /// ai寻路
    /// </summary>
    /// <param name="targetPos"></param>
    /// <param name="speed"></param>
    /// <param name="complete"></param>
    public void AINavMove(Vector2 targetPos, float speed = 1, UnityAction complete = null)
    {
        this.targetPos = targetPos;
        if (NavAgent == null) return;
        NavAgent.Speed = speed;
//Debug.Log($"移动速度读表的值={speed}，实际移动速度={NavAgent.Speed}");
        NavAgent.SetDestination(targetPos, complete);
        monster?.PlayAnimation("move01", true);
    }

    /// <summary>
    /// 开始、恢复移动
    /// </summary>
    public void StartMove()
    {
        isMoving = true;
    }
    /// <summary>
    /// 停止移动
    /// </summary>
    public void StopMoveAI()
    {
        isMoving = false;
        NavAgent.StopNavMove();
        monster?.PlayAnimation("idle01", true);
    }

    /// <summary>
    /// 获取指定半径内的随机可行走坐标点
    /// </summary>
    /// <param name="centerPos"></param>
    /// <param name="radius"></param>
    /// <returns></returns>
    private Vector2 GetRandomWalkablePosByRadius(Vector2 centerPos,int radius)
    {
        List<Vector2> list = new List<Vector2>();
        for (int x = -radius; x <= radius; x++)
        {
            for(int y = -radius; y <= radius; y++)
            {
                if(!NavAgent.IsBlockPos(centerPos + new Vector2(x,y)))
                {
                    list.Add(centerPos + new Vector2(x, y));
                }
            }
        }
        if (list.Count > 0)
        {
            int index = Random.Range(0, list.Count);
            return list[index];
        }
        else
        {
            return centerPos;
        }
    }

    private void Wander()
    {
        //moveDir = Vector2.zero;
        //if(SingletonMgr.Instance.BattleMgr.Actor != null)
        //{
        //    float distance = Vector2.Distance(transform.position, SingletonMgr.Instance.BattleMgr.Actor.Position);
        //    // 在射程外
        //    if (distance <= gunRange)
        //    {
        //        return;//在射程内不闲逛
        //    }
        //}

        moveDir = new Vector2(Random.Range(-1.0f, 1.0f), Random.Range(-1.0f, 1.0f)).normalized;
    }

    private void Chase()
    {
        if(SingletonMgr.Instance.BattleMgr.Actor != null)
        {
            float distance = Vector2.Distance(transform.position, SingletonMgr.Instance.BattleMgr.Actor.Position);
            // 枪
            //var gun = monster.MonsterThing.Guns.Value;
            //if (gun == null || NavAgent == null) return;

            //// 枪的射程
            //var gunRange = gun.GetTotalDouble(PropType.GunRange).FirstOrDefault();

            // 在射程外
            //if (distance > gunRange)
            {
                if (!NavAgent.IsNaving)
                {
                    AINavMove(SingletonMgr.Instance.BattleMgr.PlayerActor.transform.position, moveSpeed, () => { monster?.PlayAnimation("idle01", true); });
                }
            }
        }
    }
}
