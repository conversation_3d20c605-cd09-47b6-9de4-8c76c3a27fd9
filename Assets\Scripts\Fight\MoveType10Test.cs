using UnityEngine;
using X.PB;
using Thing;
using View;

/// <summary>
/// MoveType=10移动测试脚本
/// </summary>
public class MoveType10Test : MonoBehaviour
{
    [Header("测试配置")]
    public int testDirection = 3; // 1=上, 2=下, 3=左, 4=右
    public float testSpeed = 1.0f;
    public float testStopTime = 5.0f;
    
    [Header("测试对象")]
    public GameObject monsterPrefab;
    public Transform spawnPoint;
    
    private MonsterBase testMonster;
    
    void Start()
    {
        Debug.Log("[MoveType10测试] 开始测试");
        CreateTestMonster();
    }
    
    void CreateTestMonster()
    {
        if (monsterPrefab == null || spawnPoint == null)
        {
            Debug.LogError("[MoveType10测试] 缺少必要的预制体或生成点");
            return;
        }
        
        // 创建测试怪物
        GameObject monsterObj = Instantiate(monsterPrefab, spawnPoint.position, spawnPoint.rotation);
        testMonster = monsterObj.GetComponent<MonsterBase>();
        
        if (testMonster == null)
        {
            Debug.LogError("[MoveType10测试] 怪物预制体缺少MonsterBase组件");
            return;
        }
        
        // 创建测试用的BattleBrushEnemy配置
        var testConfig = new BattleBrushEnemy.Item
        {
            Id = 999999,
            EnemyName = "MoveType10测试怪物",
            MoveType = 10,
            MoveParams = new float[] { testDirection, testSpeed, testStopTime },
            Hp = 100,
            PhysicsAttack = 10,
            MoveSpeed = 100000 // 基础移动速度
        };
        
        // 创建MonsterThing并设置配置
        var monsterThing = new MonsterThing("TestMonster")
        {
            CsvRow_BattleBrushEnemy = testConfig,
            ThingLvl = { Value = 1 },
            Hp = { Value = testConfig.Hp },
            Position = spawnPoint.position
        };
        
        // 设置怪物的Thing
        testMonster.Thing = monsterThing;
        
        Debug.Log($"[MoveType10测试] 创建测试怪物成功 - 方向:{testDirection}, 速度:{testSpeed}, 停留时间:{testStopTime}");
        Debug.Log($"[MoveType10测试] 预期移动方向: {GetDirectionName(testDirection)}");
    }
    
    string GetDirectionName(int direction)
    {
        switch (direction)
        {
            case 1: return "向上";
            case 2: return "向下";
            case 3: return "向左";
            case 4: return "向右";
            default: return "未知";
        }
    }
    
    void Update()
    {
        // 按键测试
        if (Input.GetKeyDown(KeyCode.R))
        {
            Debug.Log("[MoveType10测试] 重新创建测试怪物");
            if (testMonster != null)
            {
                DestroyImmediate(testMonster.gameObject);
            }
            CreateTestMonster();
        }
        
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            testDirection = 1;
            Debug.Log("[MoveType10测试] 切换到向上移动");
            RecreateMonster();
        }
        
        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            testDirection = 2;
            Debug.Log("[MoveType10测试] 切换到向下移动");
            RecreateMonster();
        }
        
        if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            testDirection = 3;
            Debug.Log("[MoveType10测试] 切换到向左移动");
            RecreateMonster();
        }
        
        if (Input.GetKeyDown(KeyCode.Alpha4))
        {
            testDirection = 4;
            Debug.Log("[MoveType10测试] 切换到向右移动");
            RecreateMonster();
        }
    }
    
    void RecreateMonster()
    {
        if (testMonster != null)
        {
            DestroyImmediate(testMonster.gameObject);
        }
        CreateTestMonster();
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("MoveType=10 移动测试");
        GUILayout.Label($"当前方向: {GetDirectionName(testDirection)}");
        GUILayout.Label($"移动速度: {testSpeed}");
        GUILayout.Label($"停留时间: {testStopTime}秒");
        GUILayout.Space(10);
        GUILayout.Label("按键说明:");
        GUILayout.Label("R - 重新创建怪物");
        GUILayout.Label("1 - 向上移动");
        GUILayout.Label("2 - 向下移动");
        GUILayout.Label("3 - 向左移动");
        GUILayout.Label("4 - 向右移动");
        GUILayout.EndArea();
    }
}
