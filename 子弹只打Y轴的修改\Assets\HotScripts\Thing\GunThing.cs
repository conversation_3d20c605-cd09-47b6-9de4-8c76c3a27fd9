﻿// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;
using Apq.Utils;

using CsvTables;

using DataStructure;

using JsonStructure;

using Props;

using ThingCdExecutors;

using UnityEngine;

using View;

using X.PB;

namespace Thing
{
    /// <summary>
    ///     枪的基类
    /// </summary>
    /// <remarks>
    ///     数据初始化顺序：
    ///     生物
    ///     枪的等级
    ///     CsvRow_Gun
    ///     之后由生物控制
    /// </remarks>
    public class GunThing : ThingBase
    {
        public GunThing(object key = default, IBubbleNode parent = null) : base(key, parent)
        {
            WeaponLvl = new BubblingList<int>(nameof(WeaponLvl), this);
            CsvRow_Gun = new BubblingList<GunCfg.Types.CSVRow>(nameof(CsvRow_Gun), this);

            CsvRow_Gun.Changed += CsvRow_Gun_Changed;
        }

        /// <summary>
        ///     物件类型
        /// </summary>
        public override ThingType ThingType { get; set; } = ThingType.Gun;

        /// <summary>
        ///     是否为隐藏的枪
        /// </summary>
        public bool IsHidden { get; set; }

        /// <summary>
        ///     哪个角色的枪
        /// </summary>
        public ActorThing Actor => Owner as ActorThing;

        /// <summary>
        ///     哪个怪物的枪
        /// </summary>
        public MonsterThing Monster => Owner as MonsterThing;

        /// <summary>
        ///     武器等级(星量)
        /// </summary>
        public BubblingList<int> WeaponLvl { get; }

        /// <summary>
        ///     枪械配置行
        /// </summary>
        public BubblingList<GunCfg.Types.CSVRow> CsvRow_Gun { get; }

        #region IDisposable

        /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
        protected override void Dispose(bool disposing)
        {
            if (disposedValue)
            {
                return;
            }

            if (disposing)
            {
                CsvRow_Gun.Changed -= CsvRow_Gun_Changed;
            }

            // 释放未托管的资源(未托管的对象)并重写终结器
            // 将大型字段设置为 null
            base.Dispose(disposing);
        }

        #endregion

        /// <summary>
        ///     是否可升级
        /// </summary>
        /// <returns></returns>
        public virtual bool CanLvlUp()
        {
            return
                // 包含攻防血量类
                CsvRow_Gun.Value.GunType is ActionType.Attack or ActionType.Mattack
                    or ActionType.Hp or ActionType.Armor
                // 最高级写死为4级
                && ThingLvl.Value <= 3;
        }

        /// <summary>
        ///     能否将属性附着于枪
        /// </summary>
        public override bool CanAttach(CommonProp prop)
        {
            if (prop.AttachedType != AttachedType.Gun)
            {
                return false;
            }

            // AttachTo是否满足
            if (!prop.AttachTo.Contains(CsvRow_Gun.Value.Id))
            {
                return false;
            }

            // 枪的武器等级没达标
            if (WeaponLvl.Value + 2 < prop.AttachPremissMinWeaponLvl)
            {
                return false;
            }

            // 枪的等级没达标
            if (ThingLvl.Value < prop.AttachPremissMinGunLvl)
            {
                return false;
            }

            if (Actor != null && prop.AttachPremiseGoodsID > 0)
            {
                // 没有达标的装备
                if (!Actor.HasEquip(prop.AttachPremiseGoodsID, prop.AttachPremiseMinStarExp))
                {
                    return false;
                }
            }

            // 运行到这里，就是可附着了
            return true;
        }

        /// <summary>
        ///     能否将属性应用于枪
        /// </summary>
        public override bool CanApply(CommonProp prop)
        {
            if (prop.AttachedThing is BuffThing buff)
            {
                // 不是Buff的承受者
                if (prop.ApplyType == ApplyType.BuffBearer && Util.IsEquals(buff.Bearer, this))
                {
                    return false;
                }
            }

            if (prop.ApplyType == ApplyType.Attached)
            {
                // 不是被附者
                if (!Util.IsEquals(prop.AttachedThing, this))
                {
                    return false;
                }
            }
            else
            {
                // ApplyTo是否满足
                bool isApplyToMatched = prop.ApplyType switch
                {
                    ApplyType.Any => true,
                    ApplyType.GunId or ApplyType.NearGunId => prop.ApplyTo.Contains(CsvRow_Gun.Value.Id),
                    ApplyType.GunType or ApplyType.NearGunType => prop.ApplyTo.Contains((int)CsvRow_Gun.Value.GunType),
                    ApplyType.GunAll or ApplyType.NearGunAll => true,
                    _ => false
                };
                if (!isApplyToMatched)
                {
                    return false;
                }
            }

            // 应用于邻近枪械的属性，是否邻近
            if (prop.ApplyType is ApplyType.NearGunId or ApplyType.NearGunType or ApplyType.NearGunAll
                && prop.AttachedThing is GunThing gun && gun != this)
            {
                // 所有邻近的格子坐标
                List<BagPosition> nears = gun.PosInBag.SelectMany(p => new List<BagPosition>
                {
                    new(p.BagX - 1, p.BagY),
                    new(p.BagX, p.BagY - 1),
                    new(p.BagX + 1, p.BagY),
                    new(p.BagX, p.BagY + 1)
                }).Distinct().ToList();

                if (!PosInBag.Any(p => nears.Contains(p)))
                {
                    return false;
                }
            }

            // 枪的武器等级没达标
            if (WeaponLvl.Value + 2 < prop.ApplyPremiseMinWeaponLvl)
            {
                return false;
            }

            // 枪的等级没达标
            if (ThingLvl.Value < prop.ApplyPremiseMinGunLvl)
            {
                return false;
            }

            if (Actor != null && prop.ApplyPremiseGoodsID > 0)
            {
                // 没有达标的装备
                if (!Actor.HasEquip(prop.ApplyPremiseGoodsID, prop.ApplyPremiseMinStarExp))
                {
                    return false;
                }
            }

            // 运行到这里，就是可应用了
            return true;
        }

        #region 读取配置

        /// <summary>
        ///     初始化(读取csv表格中的一行)
        /// </summary>
        public GunThing InitFromCsv(int gunId, int gunLvl, int weaponLvl)
        {
            if (gunId <= 0 || gunLvl <= 0)
            {
                return this;
            }

            ThingLvl.Value = gunLvl;
            WeaponLvl.Value = weaponLvl;
            CsvRow_Gun.Value = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[gunId];

            return this;
        }

        #endregion

        /// <inheritdoc />
        public override bool PickProps()
        {
            // 从自己的附着属性中提取
            bool rtn = base.PickProps();

            if (Owner != null)
            {
                // 从其它枪的附着属性中提取
                List<CommonProp> lst1 = Owner.Guns.Where(g => g.Guid != Guid)
                    .SelectMany(g => g.AttachedProps.FindPropsCanApplyTo(this)).ToList();
                lst1.ForEach(p => AddProp(p));
                if (!rtn)
                {
                    rtn = lst1.Count > 0;
                }

                // 从物件的附着属性中提取
                List<CommonProp> lst2 = Owner.AttachedProps.FindPropsCanApplyTo(this);
                lst2.ForEach(p => AddProp(p));
                if (!rtn)
                {
                    rtn = lst2.Count > 0;
                }
            }

            return rtn;
        }

        /// <summary>
        ///     计算提升系数
        /// </summary>
        /// <param name="hoistProp">提升类属性</param>
        public override double CalcHoistCoe(CommonProp hoistProp)
        {
            double rtn = 1;
            if (Owner != null)
            {
                rtn = hoistProp.HoistCoeMethod switch
                {
                    HoistCoeMethod.GunCount => Owner.Guns.Count(p => !p.IsHidden),
                    HoistCoeMethod.LossHpPct => 1 - (Owner.Hp.Value /
                                                     Owner.GetTotalDouble(PropType.MaxHp).FirstOrDefault()),
                    _ => 1
                };
            }

            return rtn;
        }

        private void CsvRow_Gun_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is GunCfg.Types.CSVRow csvRow_Gun)
            {
                InitFromCsv(csvRow_Gun.Id, ThingLvl.Value, WeaponLvl.Value);
            }
        }

        protected override void ThingLvl_Changed(ChangeEventArgs e)
        {
            if (CsvRow_Gun.HasValue)
            {
                InitFromCsv(CsvRow_Gun.Value.Id, (int)e.NewValue, WeaponLvl.Value);
            }
        }

        /// <summary>
        ///     找出汇总到生物总属性中的总属性
        /// </summary>
        public List<AggProp> FindTotalPropsForCreature()
        {
            return TotalProps.Where(kvp =>
                    kvp.Value.PropType is PropType.MaxHp
                        or PropType.DamageReduce
                        or PropType.PriceReduce
                        or PropType.PriceReducePct
                //or PropType.Gun2Priority
            ).Select(kvp => kvp.Value).ToList();
        }

        /// <inheritdoc />
        public override DistanceThing FindEnemy()
        {
            DistanceThing rtn = base.FindEnemy();

            // 怪物的枪
            if (Owner is MonsterThing monster)
            {
                rtn.Thing2 = SingletonMgr.Instance.BattleMgr.Actor;
                /*rtn.Distance =
                    monster.CircularArea2D.CalcMinDistance(SingletonMgr.Instance.BattleMgr.Actor.CircularArea2D);*/
                // 取与怪物同列(x相同)的中心位置到怪物的最近距离
                rtn.Distance = Mathf.Abs(monster.Position.y - SingletonMgr.Instance.BattleMgr.Actor.Position.y) - monster.CircularArea2D.Radius;
            }
            // 角色的枪
            else if (Owner is ActorThing)
            {
                var monsters = SingletonMgr.Instance.BattleMgr.FindMonster(Owner.Position, Owner.TotalProp_Radius, CsvRow_Gun.Value.FindActionTarget, TotalProp_GunRange);
                if(monsters != null)
                {
                    DistanceThing distanceMonster = monsters.FirstOrDefault();
                    if (distanceMonster != null)
                    {
                        rtn = distanceMonster;
                    }
                }
            }

            return rtn;
        }

        /// <inheritdoc />
        protected override void DoCdExecutor(CancellationToken token)
        {
            // 枪所属物件已死
            if (Owner.Hp.Value <= float.Epsilon)
            {
                // 结束调度
                CTS_CdExecutor.Cancel();
                return;
            }

            // 创建执行者并启动
            ThingCdExecutor shooter = CreateCdExecutor();
            shooter?.StartExecutor(token).Forget();
        }

        /// <inheritdoc />
        public override BulletThing CreateBullet(ThingCdExecutor shooter, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, float angle, int penetrateTimes, int bounceTimes, int separateTimes,
            ICollection<MonsterThing> monsters = null
        )
        {
            return Owner.CreateBullet(shooter, attackBaseDirFollowThing, trackPos, angle, penetrateTimes,
                bounceTimes, separateTimes, monsters);
        }

        #region 固有属性

        /// <inheritdoc />
        public override List<CommonProp> FindInherentPropRows()
        {
            List<CommonProp> rtn = new();

            // 是否已加载过转化的属性
            bool hadTrans = AttachedProps.HasValue;

            // 找出可能变化的固有属性,从提升后属性中减掉并移除
            if (hadTrans)
            {
                // var attackProp = AttachedProps
                //     .FirstOrDefault(p => p.Id == 0 && p.PropType == PropType.Attack);
                // if (attackProp != null)
                // {
                //     SubtractProp(attackProp);
                //     AttachedProps.Remove(attackProp);
                // }

                AttachedProps.Where(p =>
                            p.Id == 0 && p.PropCatalog == PropCatalog.Weapon
                        // p.PropType is PropType.MaxHp or PropType.HpRally
                        //     or PropType.HpRallyPct or PropType.ArmorRally or PropType.ArmorRallyPct
                        //     or PropType.Model
                    )
                    .ToList().ForEach(p =>
                    {
                        SubtractProp(p);
                        AttachedProps.Remove(p);
                    });
            }

            // 重新加载这些可能变化的属性
            {
                // 枪等级换算为属性值加成的底数
                double lvlBaseCoe = 1.5;

                // 角色的枪
                if (Actor != null)
                {
                    // 将装备属性转为通用属性
                    CreaturePropBase weaponProp =
                        LuaDataSrvClient.Instance.GetWeaponProp(CsvRow_Gun.Value.GoodsID)?.EquipProp ??
                        LuaDataSrvClient.Instance.ReadStarProp(CsvRow_Gun.Value.GoodsID, -1);

                    // MaxHp
                    double maxHp = weaponProp.HP;
                    if (maxHp > 0)
                    {
                        maxHp *= Math.Pow(1.35, ThingLvl.Value - 1);
                        rtn.Add(
                            new CommonProp
                            {
                                AttachedType = AttachedType.Gun,
                                AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                                PropCatalog = PropCatalog.Weapon,
                                PropType = PropType.MaxHp,
                                ValueType = CsValueType.Double,
                                ApplyType = ApplyType.Attached
                            }.SetDouble(maxHp)
                        );
                    }

                    // Attack
                    double attack = weaponProp.PhysicsAttack;
                    if (attack > 0)
                    {
                        attack *= Math.Pow(lvlBaseCoe, ThingLvl.Value - 1);
                        rtn.Add(
                            new CommonProp
                            {
                                AttachedType = AttachedType.Gun,
                                AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                                PropCatalog = PropCatalog.Weapon,
                                PropType = PropType.Attack,
                                ValueType = CsValueType.Double,
                                ApplyType = ApplyType.Attached
                            }.SetDouble(attack)
                        );
                    }

                    // HpRally
                    double hpRally = weaponProp.MagicAttack;
                    if (hpRally > 0)
                    {
                        hpRally *= Math.Pow(lvlBaseCoe, ThingLvl.Value - 1);
                        rtn.Add(
                            new CommonProp
                            {
                                AttachedType = AttachedType.Gun,
                                AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                                PropCatalog = PropCatalog.Weapon,
                                PropType = PropType.HpRally,
                                ValueType = CsValueType.Double,
                                ApplyType = ApplyType.Attached
                            }.SetDouble(hpRally)
                        );
                    }

                    // HpRallyPct
                    double hpRallyPct = Globals.UnityValueTransform(weaponProp.PhysicsDefense);
                    if (hpRallyPct > 0)
                    {
                        hpRallyPct *= Math.Pow(lvlBaseCoe, ThingLvl.Value - 1);
                        rtn.Add(
                            new CommonProp
                            {
                                //CsvRow =
                                //{
                                AttachedType = AttachedType.Gun,
                                AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                                PropCatalog = PropCatalog.Weapon,
                                PropType = PropType.HpRallyPct,
                                ValueType = CsValueType.Double,
                                ApplyType = ApplyType.Attached
                                //}
                            }.SetDouble(hpRallyPct)
                        );
                    }

                    // ArmorRally
                    double armorRally = weaponProp.MagicDefense;
                    if (armorRally > 0)
                    {
                        armorRally *= Math.Pow(lvlBaseCoe, ThingLvl.Value - 1);
                        rtn.Add(
                            new CommonProp
                            {
                                //CsvRow =
                                //{
                                AttachedType = AttachedType.Gun,
                                AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                                PropCatalog = PropCatalog.Weapon,
                                PropType = PropType.ArmorRally,
                                ValueType = CsValueType.Double,
                                ApplyType = ApplyType.Attached
                                //}
                            }.SetDouble(armorRally)
                        );
                    }

                    // ArmorRallyPct
                    double armorRallyPct = Globals.UnityValueTransform(weaponProp.CriticalStrike);
                    if (armorRallyPct > 0)
                    {
                        armorRallyPct *= Math.Pow(lvlBaseCoe, ThingLvl.Value - 1);
                        rtn.Add(
                            new CommonProp
                            {
                                //CsvRow =
                                //{
                                AttachedType = AttachedType.Gun,
                                AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                                PropCatalog = PropCatalog.Weapon,
                                PropType = PropType.ArmorRallyPct,
                                ValueType = CsValueType.Double,
                                ApplyType = ApplyType.Attached
                                //}
                            }.SetDouble(armorRallyPct)
                        );
                    }
                }

                // 怪物的枪
                else if (Monster != null)
                {
                    // 攻击
                    double attack = Monster.CsvRow_BattleBrushEnemy.PhysicsAttack;
                    rtn.Add(
                        new CommonProp
                        {
                            AttachedType = AttachedType.Gun,
                            AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                            PropCatalog = PropCatalog.Weapon,
                            PropType = PropType.Attack,
                            ValueType = CsValueType.Double,
                            ApplyType = ApplyType.Attached
                        }.SetDouble(attack)
                    );
                }
            }

            if (!hadTrans)
            {
                // 将GunCfg.csv中的(部分)列 转为通用属性
                rtn.AddRange(new List<CommonProp>
                {
                    // ShootMethod
                    new CommonProp
                    {
                        AttachedType = AttachedType.Gun,
                        AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.ShootMethod,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached
                    }.SetLong((int)CsvRow_Gun.Value.ShootMethod),
                    // BulletId
                    new CommonProp
                    {
                        AttachedType = AttachedType.Gun,
                        AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.BulletId,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached
                    }.SetLong(CsvRow_Gun.Value.BulletId),
                    // MinCD
                    new CommonProp
                    {
                        AttachedType = AttachedType.Gun,
                        AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.MinCd,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached
                    }.SetDouble(CsvRow_Gun.Value.MinCD),
                    // CD
                    new CommonProp
                    {
                        AttachedType = AttachedType.Gun,
                        AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Cd,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached
                    }.SetDouble(CsvRow_Gun.Value.CD),
                    // 射程
                    new CommonProp
                    {
                        AttachedType = AttachedType.Gun,
                        AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.GunRange,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached
                    }.SetDouble(CsvRow_Gun.Value.GunRange),
                    // 攻击
                    new CommonProp
                    {
                        AttachedType = AttachedType.Gun,
                        AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Attack,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached
                    }.SetDouble(CsvRow_Gun.Value.Attack),
                    // 血量
                    new CommonProp
                    {
                        AttachedType = AttachedType.Gun,
                        AttachTo = new List<int> { CsvRow_Gun.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.MaxHp,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached
                    }.SetDouble(CsvRow_Gun.Value.MaxHp)
                });
            }

            // 从所有固有属性中找出附着于该枪的属性
            rtn.AddRange(
                SingletonMgr.Instance.GlobalMgr.CommonPropCfg.Values.FindPropsCanAttachTo(this, PropCatalog.Inherent));

            return rtn;
        }

        /// <inheritdoc />
        public override void ReloadAttachedProps()
        {
            base.ReloadAttachedProps();

            if (Actor != null)
            {
                // 战场属性
                Actor.BattleProps.FindPropsCanAttachTo(this)
                    .ForEach(p => AddAttachedProp(p));

                // 关卡属性
                Actor.StageProps.FindPropsCanAttachTo(this)
                    .ForEach(p => AddAttachedProp(p));
            }
        }

        /// <inheritdoc />
        public override List<CreatureThing> FindCreaturesCanApply(CommonProp prop,
            bool containsSelf = false, bool excludeApplied = true)
        {
            List<CreatureThing> rtn = base.FindCreaturesCanApply(prop, containsSelf, excludeApplied);

            // 所属角色能用不
            if (Actor != null && !rtn.Contains(Actor) && Actor.CanApply(prop) &&
                (!excludeApplied || Actor.HoistedProps.ContainsProp(prop)))
            {
                rtn.Add(Actor);
            }

            // 所属怪物能用不
            if (Monster != null && !rtn.Contains(Monster) && Monster.CanApply(prop) &&
                (!excludeApplied || Monster.HoistedProps.ContainsProp(prop)))
            {
                rtn.Add(Monster);
            }

            return rtn;
        }

        /// <summary>
        ///     找出一个附着的属性可应用于哪些物件
        /// </summary>
        /// <param name="prop"></param>
        /// <param name="containsSelf">是否包含自己</param>
        /// <param name="excludeApplied">是否排除已应用的枪</param>
        public virtual List<ThingBase> FindGunsCanApply(CommonProp prop,
            bool containsSelf = false, bool excludeApplied = true)
        {
            List<ThingBase> rtn = new();

            // 哪些枪能用
            if (Owner != null)
            {
                rtn.AddRange(Owner.Guns.Where(m =>
                    m.CanApply(prop) &&
                    (!excludeApplied || !m.HoistedProps.ContainsProp(prop))));
            }

            // 自己能用不
            if (containsSelf && CanApply(prop))
            {
                rtn.Add(this);
            }

            return rtn;
        }

        /// <inheritdoc />
        public override List<ThingBase> DispatchAttachedProp(CommonProp p)
        {
            List<ThingBase> rtn = new();

            // 应用到携带的Buff
            CarriedBuffs_HitForInjurer.ToList().ForEach(b =>
            {
                if (b.CanApply(p))
                {
                    b.AddProp(p);
                    if (!rtn.Contains(b))
                    {
                        rtn.Add(b);
                    }
                }
            });
            CarriedBuffs_KilledForAttacker.ToList().ForEach(b =>
            {
                if (b.CanApply(p))
                {
                    b.AddProp(p);
                    if (!rtn.Contains(b))
                    {
                        rtn.Add(b);
                    }
                }
            });

            if (Owner != null)
            {
                // 应用到物件的每个枪
                Owner.Guns.ToList().ForEach(g =>
                {
                    if (g.CanApply(p))
                    {
                        g.AddProp(p);
                        if (!rtn.Contains(g))
                        {
                            rtn.Add(g);
                        }
                    }
                });

                // 应用到物件
                if (Owner.CanApply(p))
                {
                    Owner.AddProp(p);
                    if (!rtn.Contains(Owner))
                    {
                        rtn.Add(Owner);
                    }
                }
            }

            return rtn;
        }

        /// <inheritdoc />
        public override List<ThingBase> DispatchAttachedProp()
        {
            List<ThingBase> rtn = new();

            // 应用到携带的Buff
            CarriedBuffs_HitForInjurer.ToList().ForEach(b =>
            {
                AttachedProps.ToList().ForEach(p =>
                {
                    if (b.CanApply(p))
                    {
                        b.AddProp(p);
                        if (!rtn.Contains(b))
                        {
                            rtn.Add(b);
                        }
                    }
                });
            });
            CarriedBuffs_KilledForAttacker.ToList().ForEach(b =>
            {
                AttachedProps.ToList().ForEach(p =>
                {
                    if (b.CanApply(p))
                    {
                        b.AddProp(p);
                        if (!rtn.Contains(b))
                        {
                            rtn.Add(b);
                        }
                    }
                });
            });

            if (Owner != null)
            {
                // 应用到物件的每个枪
                Owner.Guns.ToList().ForEach(g =>
                {
                    AttachedProps.ToList().ForEach(p =>
                    {
                        if (g.CanApply(p))
                        {
                            g.AddProp(p);
                            if (!rtn.Contains(g))
                            {
                                rtn.Add(g);
                            }
                        }
                    });
                });

                // 应用到物件
                _ = AttachedProps.Where(Owner.CanApply).Select((p, i) =>
                {
                    Owner.AddProp(p);
                    if (!rtn.Contains(Owner))
                    {
                        rtn.Add(Owner);
                    }

                    return i;
                }).ToList();
            }

            return rtn;
        }

        #endregion
    }
}