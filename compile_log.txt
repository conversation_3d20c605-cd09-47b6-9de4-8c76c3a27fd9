[Licensing::Module] Channel doesn't exist: "LicenseClient-fa"
Error creating external process 'D:/Program Files/Unity 2021.2.5f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe' : 系统找不到指定的文件。
Error cleaning up external process: 句柄无效。
[Licensing::Module] Error: Failed to launch LicensingClient: D:/Program Files/Unity 2021.2.5f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe
IPC channel to LicensingClient doesn't exist; falling back to Legacy licensing

LICENSE SYSTEM [202575 22:21:44] No start/stop license dates set

LICENSE SYSTEM [202575 22:21:44] Next license update check is after 2035-05-21T16:24:10


LICENSE SYSTEM [202575 22:21:44] Current license is already valid and activated. Skipping license activation process (Provided username/password will be ignored).
Built from '2021.2/staging' branch; Version is '2021.2.5f1 (4ec9a5e799f5) revision 5163429'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32500 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
[Package Manager] Server::Start -- Port 58938 was selected

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity 2021.2.5f1\Editor\Unity.exe
-batchmode
-quit
-projectPath
e:\xiuxian\24-banhao13\Client2021-banhao13
-logFile
e:\xiuxian\24-banhao13\Client2021-banhao13\compile_log.txt
Successfully changed project path to: e:\xiuxian\24-banhao13\Client2021-banhao13
E:/xiuxian/24-banhao13/Client2021-banhao13
It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: E:/xiuxian/24-banhao13/Client2021-banhao13
Fatal Error! It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: E:/xiuxian/24-banhao13/Client2021-banhao13
Crash!!!
SymInit: Symbol-SearchPath: 'D:/Program Files/Unity 2021.2.5f1/Editor/Data/Mono;.;E:\xiuxian\24-banhao13\Client2021-banhao13;E:\xiuxian\24-banhao13\Client2021-banhao13\Library\BurstCache\JIT;D:\Program Files\Unity 2021.2.5f1\Editor;C:\WINDOWS;C:\WINDOWS\system32;SRV*C:\websymbols*http://msdl.microsoft.com/download/symbols;', symOptions: 534, UserName: 'fa'
OS-Version: 10.0.0
D:\Program Files\Unity 2021.2.5f1\Editor\Unity.exe:Unity.exe (00007FF764CA0000), size: 72966144 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2021.2.5.51621
C:\WINDOWS\SYSTEM32\ntdll.dll:ntdll.dll (00007FFFC6340000), size: 2510848 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\KERNEL32.DLL:KERNEL32.DLL (00007FFFC54C0000), size: 823296 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\KERNELBASE.dll:KERNELBASE.dll (00007FFFC3610000), size: 4096000 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\CRYPT32.dll:CRYPT32.dll (00007FFFC3490000), size: 1536000 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.3775
C:\WINDOWS\System32\ucrtbase.dll:ucrtbase.dll (00007FFFC3A90000), size: 1355776 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\USER32.dll:USER32.dll (00007FFFC52F0000), size: 1875968 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\win32u.dll:win32u.dll (00007FFFC3E90000), size: 159744 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4343
C:\WINDOWS\System32\GDI32.dll:GDI32.dll (00007FFFC4120000), size: 176128 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\gdi32full.dll:gdi32full.dll (00007FFFC3BE0000), size: 1273856 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\msvcp_win.dll:msvcp_win.dll (00007FFFC3D20000), size: 667648 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\ADVAPI32.dll:ADVAPI32.dll (00007FFFC56F0000), size: 733184 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\msvcrt.dll:msvcrt.dll (00007FFFC5590000), size: 692224 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 7.0.26100.4202
C:\WINDOWS\System32\sechost.dll:sechost.dll (00007FFFC5640000), size: 679936 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\RPCRT4.dll:RPCRT4.dll (00007FFFC43C0000), size: 1134592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\SHELL32.dll:SHELL32.dll (00007FFFC4790000), size: 7610368 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\wintypes.dll:wintypes.dll (00007FFFC3EC0000), size: 1523712 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\combase.dll:combase.dll (00007FFFC5EF0000), size: 3690496 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\SHLWAPI.dll:SHLWAPI.dll (00007FFFC5830000), size: 434176 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\ole32.dll:ole32.dll (00007FFFC5140000), size: 1695744 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\IMM32.dll:IMM32.dll (00007FFFC40E0000), size: 196608 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\SETUPAPI.dll:SETUPAPI.dll (00007FFFC58B0000), size: 4743168 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\WS2_32.dll:WS2_32.dll (00007FFFC5E70000), size: 475136 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\System32\OLEAUT32.dll:OLEAUT32.dll (00007FFFC5D80000), size: 921600 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\WINTRUST.dll:WINTRUST.dll (00007FFFC3A00000), size: 540672 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4349
D:\Program Files\Unity 2021.2.5f1\Editor\s3tcompress.dll:s3tcompress.dll (00007FFFBDC70000), size: 180224 (result: 0), SymType: '-deferred-', PDB: ''
D:\Program Files\Unity 2021.2.5f1\Editor\optix.6.0.0.dll:optix.6.0.0.dll (00007FFFBDBC0000), size: 208896 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 6.0.0.0
D:\Program Files\Unity 2021.2.5f1\Editor\ispc_texcomp.dll:ispc_texcomp.dll (00007FFF78CD0000), size: 1740800 (result: 0), SymType: '-deferred-', PDB: ''
D:\Program Files\Unity 2021.2.5f1\Editor\compress_bc7e.dll:compress_bc7e.dll (00007FFF60FF0000), size: 1441792 (result: 0), SymType: '-deferred-', PDB: ''
D:\Program Files\Unity 2021.2.5f1\Editor\etccompress.dll:etccompress.dll (00007FFF38680000), size: 5066752 (result: 0), SymType: '-deferred-', PDB: ''
D:\Program Files\Unity 2021.2.5f1\Editor\OpenImageDenoise.dll:OpenImageDenoise.dll (00007FFF04A50000), size: 35123200 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.3.0.0
D:\Program Files\Unity 2021.2.5f1\Editor\WinPixEventRuntime.dll:WinPixEventRuntime.dll (00007FFFBEE30000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.0.1812.6001
C:\WINDOWS\SYSTEM32\OPENGL32.dll:OPENGL32.dll (00007FFF93B10000), size: 1130496 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\GLU32.dll:GLU32.dll (00007FFF936E0000), size: 184320 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL:IPHLPAPI.DLL (00007FFFC1D10000), size: 208896 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\WINHTTP.dll:WINHTTP.dll (00007FFFBBC50000), size: 1171456 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\WINMM.dll:WINMM.dll (00007FFFB9B30000), size: 217088 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\bcrypt.dll:bcrypt.dll (00007FFFC3370000), size: 155648 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\SYSTEM32\HID.DLL:HID.DLL (00007FFFC19B0000), size: 61440 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\dwmapi.dll:dwmapi.dll (00007FFFC0730000), size: 221184 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
D:\Program Files\Unity 2021.2.5f1\Editor\umbraoptimizer64.dll:umbraoptimizer64.dll (00007FFF89CB0000), size: 1187840 (result: 0), SymType: '-deferred-', PDB: ''
D:\Program Files\Unity 2021.2.5f1\Editor\OpenRL.dll:OpenRL.dll (0000000180000000), size: 12779520 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.5.0.2907
D:\Program Files\Unity 2021.2.5f1\Editor\SketchUpAPI.dll:SketchUpAPI.dll (00007FFF04170000), size: 9306112 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 20.2.172.0
C:\WINDOWS\SYSTEM32\VERSION.dll:VERSION.dll (00007FFFB0FA0000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\WSOCK32.dll:WSOCK32.dll (00007FFFBEE20000), size: 40960 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
D:\Program Files\Unity 2021.2.5f1\Editor\RadeonImageFilters.dll:RadeonImageFilters.dll (00007FFF5CA10000), size: 2961408 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.7.0.0
C:\WINDOWS\SYSTEM32\MSVCP140.dll:MSVCP140.dll (00007FFF52800000), size: 577536 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.42.34438.0
C:\WINDOWS\SYSTEM32\VCRUNTIME140.dll:VCRUNTIME140.dll (00007FFF1F950000), size: 122880 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.42.34438.0
C:\WINDOWS\SYSTEM32\MSVCP100.dll:MSVCP100.dll (0000000064F70000), size: 622592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.40219.325
C:\WINDOWS\SYSTEM32\MSVCR100.dll:MSVCR100.dll (0000000064E90000), size: 860160 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.40219.325
D:\Program Files\Unity 2021.2.5f1\Editor\libfbxsdk.dll:libfbxsdk.dll (00007FFEF4E10000), size: 10215424 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2020.2.0.0
D:\Program Files\Unity 2021.2.5f1\Editor\tbb12.dll:tbb12.dll (00007FFFA35E0000), size: 389120 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\SYSTEM32\Secur32.dll:Secur32.dll (00007FFFBF4D0000), size: 53248 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\WINDOWS\SYSTEM32\VCRUNTIME140_1.dll:VCRUNTIME140_1.dll (00007FFF8A390000), size: 49152 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.42.34438.0
C:\WINDOWS\SYSTEM32\dxcore.dll:dxcore.dll (00007FFFBF940000), size: 315392 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
D:\Program Files\Unity 2021.2.5f1\Editor\RadeonML.dll:RadeonML.dll (00007FFFBDCC0000), size: 94208 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 0.9.11.0
D:\Program Files\Unity 2021.2.5f1\Editor\SketchUpCommonPreferences.dll:SketchUpCommonPreferences.dll (00007FFFA3540000), size: 479232 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 20.2.172.37
C:\WINDOWS\SYSTEM32\SSPICLI.DLL:SSPICLI.DLL (00007FFFC2610000), size: 299008 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
D:\Program Files\Unity 2021.2.5f1\Editor\embree.dll:embree.dll (00007FFEF3E20000), size: 16711680 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2.14.0.0
D:\Program Files\Unity 2021.2.5f1\Editor\tbb.dll:tbb.dll (00007FFF89C00000), size: 413696 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2017.0.2016.1004
C:\WINDOWS\SYSTEM32\MSVCR120.dll:MSVCR120.dll (00007FFF60CE0000), size: 978944 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 12.0.40660.0
C:\WINDOWS\SYSTEM32\MSVCP120.dll:MSVCP120.dll (00007FFF84E70000), size: 679936 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 12.0.40660.0
D:\Program Files\Unity 2021.2.5f1\Editor\FreeImage.dll:FreeImage.dll (000001B4AFF70000), size: 6582272 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 3.18.0.0
D:\Program Files\Unity 2021.2.5f1\Editor\OpenRL_pthread.dll:OpenRL_pthread.dll (000001B4B05C0000), size: 61440 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2.9.0.0
C:\WINDOWS\SYSTEM32\cfgmgr32.DLL:cfgmgr32.DLL (00007FFFC3120000), size: 356352 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\MSWSOCK.DLL:MSWSOCK.DLL (00007FFFC28E0000), size: 434176 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
D:\Program Files\Unity 2021.2.5f1\Editor\tbbmalloc.dll:tbbmalloc.dll (00007FFF7FFA0000), size: 372736 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2017.0.2016.1004
C:\WINDOWS\SYSTEM32\MSASN1.dll:MSASN1.dll (00007FFFC2C00000), size: 77824 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\SYSTEM32\kernel.appcore.dll:kernel.appcore.dll (00007FFFC2310000), size: 110592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\bcryptPrimitives.dll:bcryptPrimitives.dll (00007FFFC4040000), size: 626688 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\system32\uxtheme.dll:uxtheme.dll (00007FFFBF760000), size: 716800 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\shcore.dll:shcore.dll (00007FFFC4150000), size: 987136 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\windows.storage.dll:windows.storage.dll (00007FFFC1150000), size: 8749056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4343
C:\WINDOWS\SYSTEM32\profapi.dll:profapi.dll (00007FFFC33A0000), size: 192512 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\clbcatq.dll:clbcatq.dll (00007FFFC4550000), size: 688128 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2001.12.10941.16384
C:\WINDOWS\system32\wbem\wbemprox.dll:wbemprox.dll (00007FFF9E1C0000), size: 73728 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\wbemcomn.dll:wbemcomn.dll (00007FFFA9F40000), size: 499712 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\system32\wbem\wbemsvc.dll:wbemsvc.dll (00007FFF951A0000), size: 86016 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\system32\wbem\fastprox.dll:fastprox.dll (00007FFF935C0000), size: 983040 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.3624
C:\WINDOWS\SYSTEM32\amsi.dll:amsi.dll (00007FFF931C0000), size: 118784 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\USERENV.dll:USERENV.dll (00007FFFC2980000), size: 176128 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.2454
C:\ProgramData\Microsoft\Windows Defender\Platform\4.18.25050.5-0\MpOav.dll:MpOav.dll (00007FFF8FF80000), size: 634880 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 4.18.25050.5
C:\WINDOWS\system32\IconCodecService.dll:IconCodecService.dll (00007FFF8B450000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\WINDOWS\SYSTEM32\WindowsCodecs.dll:WindowsCodecs.dll (00007FFFBE260000), size: 2330624 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\netprofm.dll:netprofm.dll (00007FFFBD480000), size: 413696 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.2454
C:\WINDOWS\System32\npmproxy.dll:npmproxy.dll (00007FFFB4060000), size: 102400 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\dbghelp.dll:dbghelp.dll (00007FFFC04A0000), size: 2363392 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202

========== OUTPUTTING STACK TRACE ==================

0x00007FFFC36D85EA (KERNELBASE) RaiseException
0x00007FF7666C68EB (Unity) EditorMonoConsole::LogToConsoleImplementation
0x00007FF7666C73DD (Unity) EditorMonoConsole::LogToConsoleImplementation
0x00007FF7672A2C37 (Unity) DebugStringToFilePostprocessedStacktrace
0x00007FF7672A23CD (Unity) DebugStringToFile
0x00007FF7667DA5A4 (Unity) HandleProjectAlreadyOpenInAnotherInstance
0x00007FF7667DCE1C (Unity) Application::InitializeProject
0x00007FF766C29DE8 (Unity) WinMain
0x00007FF767F392C2 (Unity) __scrt_common_main_seh
0x00007FFFC54EE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFFC637C34C (ntdll) RtlUserThreadStart

========== END OF STACKTRACE ===========

A crash has been intercepted by the crash handler. For call stack and other details, see the latest crash report generated in:
 * C:/Users/<USER>/AppData/Local/Temp/Unity/Editor/Crashes
